<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTaskRequest;
use App\Http\Resources\TaskResource;
use App\Models\Contract;
use App\Models\Task;
use App\Models\User;
use App\Models\Worker;
use App\TaskStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use function Laravel\Prompts\select;

class TaskController extends Controller
{
    public function nodeTasks($id, Request $request)
    {
        $perPage = $request->query('perPage', 10);

//        $userId = auth()->id();
//        $childTasks = Task::query()
//            ->where('worker_id', $id)
//            ->orderByDesc('id')
//            ->paginate($perPage)
//        ;

//        მოკლედ მაქვს ორი სვეტი master_user_id და user_id, ანუ ვისი დაწერილია ვისთვის
//        მაქვს დალოგინებული იუზერის აიდი, მინდა თუ ეს აიდი ემთხვევა master_user_id -ს მაშინ
//        მონაცემები გაფილტროს ->where('is_active', true) ით, ხოლო თუ აიდი ემთხვევა user_id -ს
//        მაშინ საერთოდ არ დაადო ეს შეზღუდვა

        $userId = auth()->id();
        $workerId = $id;

//        $childTasks = Task::query()
//            ->where('worker_id', $workerId)
//            ->orderByDesc('id')
//            ->paginate($perPage)
//        ;

        $childTasks = Task::query()
            ->join('workers', 'workers.id', '=', 'tasks.worker_id')
            ->where('tasks.worker_id', $workerId)
            ->when(true, function ($query) use ($userId) {
                $query->where(function ($query) use ($userId) {
                    $query->where(function ($q) use ($userId) {
                        // თუ user_id ემთხვევა
                        $q->where('workers.user_id', $userId)
                            ->where('tasks.is_active', true);
                    })->orWhere(function ($q) use ($userId) {
                        // თუ parent_master_user_id ემთხვევა
                        $q->where('workers.parent_master_user_id', $userId);
                    });
                });
            })
            ->orderByDesc('tasks.id')
            ->select('tasks.*') // აირჩიე მხოლოდ Task-ის სვეტები
            ->paginate($perPage);



        return TaskResource::collection($childTasks);
    }

    public function storeTask(StoreTaskRequest $request)
    {
        $validated = $request->validated();

        $data = [
            'status_id' => TaskStatus::STATUS_STARTED,

            'worker_id' => $validated['worker_id'],

            'type' => $validated['type'],

            'duration' => $validated['duration'] ?? null,
            'started_at' => now(),

            'task_description' => $validated['task_description'],
            'task_file' => $validated['task_file'] ?? null,
            'is_active' => $validated['is_active'] ?? true,
        ];

        // Only add schedule fields if type is 0 (recurring)
        if ($validated['type'] == 0) {
            $data['weekday'] = $validated['weekday'];
            $data['hour'] = $validated['hour'];
            $data['minute'] = $validated['minute'];
            $data['second'] = $validated['second'] ?? 0;
        }

        $task = Task::query()->create($data);

        return response()->json([
            'message' => 'Task created successfully.',
            'data' => $task,
        ], 201);
    }


}
