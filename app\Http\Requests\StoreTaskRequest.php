<?php

namespace App\Http\Requests;

use App\Models\Worker;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'worker_id' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) {
                    $exists = Worker::query()
                        ->where('id', $value)
                        ->where('user_id', auth()->id())
                        ->exists()
                    ;

                    if ($exists) {
                        $fail('you can not order yourself');
                    }
                },
            ],

            'type' => ['required', Rule::in([0, 1])], // 0: timelapse (weekly), 1: perlapse (one-time)

// For type=0 (recurring) - make these required
            'weekday' => 'required_if:type,0|nullable|integer|min:0|max:6',
            'hour' => 'required_if:type,0|nullable|integer|min:0|max:23',
            'minute' => 'required_if:type,0|nullable|integer|min:0|max:59',
            'second' => 'nullable|integer|min:0|max:59',

// For both types, but required for type=1
            'duration' => 'required_if:type,1|nullable|integer|min:0|max:23',


            'task_description' => 'required|string',
            'task_file' => 'nullable|string',

            'is_active' => 'required|integer|between:0,1',
        ];
    }
}
