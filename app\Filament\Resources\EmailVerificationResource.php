<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailVerificationResource\Pages;
use App\Filament\Resources\EmailVerificationResource\RelationManagers;
use App\Models\EmailVerification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EmailVerificationResource extends Resource
{
    protected static ?string $model = EmailVerification::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static ?string $navigationLabel = 'Email Verifications';

    protected static ?string $modelLabel = 'Email Verification';

    protected static ?string $pluralModelLabel = 'Email Verifications';

    protected static ?string $navigationGroup = 'Verification';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Email Verification Information')
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->helperText('Email address to verify'),
                        Forms\Components\TextInput::make('token')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Verification token')
                            ->columnSpanFull(),
                        Forms\Components\DateTimePicker::make('expires_at')
                            ->required()
                            ->helperText('When this verification token expires'),
                        Forms\Components\Toggle::make('verified')
                            ->default(false)
                            ->helperText('Whether this email has been verified'),
                        Forms\Components\DateTimePicker::make('verified_at')
                            ->helperText('When this email was verified')
                            ->visible(fn (Forms\Get $get) => $get('verified')),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('token')
                    ->label('Verification Token')
                    ->limit(20)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        return $column->getState();
                    })
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('verified')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('verified_at')
                    ->label('Verified At')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not verified'),
                Tables\Columns\TextColumn::make('expires_at')
                    ->label('Expires At')
                    ->dateTime()
                    ->sortable()
                    ->color(fn ($record) => $record->expires_at->isPast() ? 'danger' : 'success'),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->getStateUsing(function ($record) {
                        if ($record->verified) {
                            return 'Verified';
                        } elseif ($record->expires_at->isPast()) {
                            return 'Expired';
                        } else {
                            return 'Pending';
                        }
                    })
                    ->colors([
                        'success' => 'Verified',
                        'danger' => 'Expired',
                        'info' => 'Pending',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('verified')
                    ->label('Verified Only')
                    ->query(fn (Builder $query): Builder => $query->where('verified', true))
                    ->toggle(),
                Filter::make('unverified')
                    ->label('Unverified Only')
                    ->query(fn (Builder $query): Builder => $query->where('verified', false))
                    ->toggle(),
                Filter::make('expired')
                    ->label('Expired Only')
                    ->query(fn (Builder $query): Builder => $query->where('expires_at', '<', now()))
                    ->toggle(),
                Filter::make('active')
                    ->label('Active Only')
                    ->query(fn (Builder $query): Builder => $query->where('verified', false)->where('expires_at', '>', now()))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('resend_token')
                    ->label('Resend Token')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->action(function (EmailVerification $record) {
                        // Create new verification token
                        EmailVerification::createForEmail($record->email);
                        // Optionally delete the old one or mark as expired
                    })
                    ->visible(fn (EmailVerification $record) => !$record->verified),
                Tables\Actions\Action::make('mark_verified')
                    ->label('Mark as Verified')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(fn (EmailVerification $record) => $record->markAsVerified())
                    ->visible(fn (EmailVerification $record) => !$record->verified),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_verified')
                        ->label('Mark as Verified')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(fn ($records) => $records->each->markAsVerified()),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailVerifications::route('/'),
            'create' => Pages\CreateEmailVerification::route('/create'),
            'view' => Pages\ViewEmailVerification::route('/{record}'),
            'edit' => Pages\EditEmailVerification::route('/{record}/edit'),
        ];
    }
}
