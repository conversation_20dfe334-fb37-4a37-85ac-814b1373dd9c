<div class="border-l-2 border-gray-200 pl-4 {{ $level > 0 ? 'ml-4' : '' }}">
    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center space-x-3">
            <!-- Level indicator -->
            <div class="flex-shrink-0">
                @if($level === 0)
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                @elseif($level === 1)
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                @else
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                @endif
            </div>
            
            <!-- Worker info -->
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <h4 class="font-medium text-gray-900">{{ $worker->position }}</h4>
                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ $worker->slug }}</span>
                </div>
                
                <div class="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                    @if($worker->user)
                        <span class="flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="text-green-600 font-medium">{{ $worker->user->name }}</span>
                        </span>
                    @else
                        <span class="flex items-center space-x-1 text-yellow-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span>Unassigned</span>
                        </span>
                    @endif
                    
                    <span class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        <span>{{ number_format($worker->reward, 3) }}%</span>
                    </span>
                    
                    @if($worker->children->count() > 0)
                        <span class="flex items-center space-x-1 text-blue-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.196-2.196M17 20v-2a3 3 0 00-3-3H8a3 3 0 00-3 3v2m14 0h-5m-9 0H3m9-10a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>{{ $worker->children->count() }} children</span>
                        </span>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center space-x-2">
            <a href="{{ \App\Filament\Resources\WorkerResource::getUrl('view', ['record' => $worker]) }}" 
               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View
            </a>
            <a href="{{ \App\Filament\Resources\WorkerResource::getUrl('edit', ['record' => $worker]) }}" 
               class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                Edit
            </a>
        </div>
    </div>
    
    <!-- Children -->
    @if($worker->children->count() > 0)
        <div class="mt-2 space-y-2">
            @foreach($worker->children as $child)
                @include('filament.widgets.partials.worker-tree-node', ['worker' => $child, 'level' => $level + 1])
            @endforeach
        </div>
    @endif
</div>
