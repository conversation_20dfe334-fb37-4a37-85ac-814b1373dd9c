<?php

namespace App\Http\Resources;

use App\Http\Resources\Applicant\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function Laravel\Prompts\select;

class NetworkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $uer = $this->user_id;
//        $isMine = (bool)($uer == auth()->id());
//        $masterUser = null;
//        if($isMine){
//            $masterUser = User::query()
//                ->select(['id',
//                    'leave_count',
//                    'reject_count',
//                    'rate',])
//                ->find($this->parent_master_user_id);
//        }

        return [
            'id' => $this->id,
            'position' => $this->position,
            'slug' => $this->slug,
            'description' => $this->description,
            'reward' => $this->reward,
            'contract_id' => $this->contract_id,
            'parent_id' => $this->parent_id,
            'pointer_status_id' => $this->pointer_status_id,

            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'contracts_count' => $this->contracts_count,
            'pending_contract_exists' => $this->pending_contract_exists,

            'children' => self::collection($this->whenLoaded('children')),
            'contract' => ContractResource::make($this->whenLoaded('contract')),
            'contracts_exists' => $this->contracts_exists,
        ];
    }
}
