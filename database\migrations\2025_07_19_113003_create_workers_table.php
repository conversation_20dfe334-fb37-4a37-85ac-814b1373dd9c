<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Kalnoy\Nestedset\NestedSet;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workers', function (Blueprint $table) { // position or company
            $table->id();
            $table->unsignedTinyInteger('pointer_status_id')->default(1)->index();
            $table->string('position');
            $table->string('slug'); // from position
            $table->text('description')->nullable();
            $table->decimal('reward')->comment('in %');
            $table->decimal('network_balance', 15, 4)->default(0);

            NestedSet::columns($table); // ეს ქმნის: parent_id, lft, rgt, depth // ვინ აკონტროლებს მას
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workers');
    }
};
