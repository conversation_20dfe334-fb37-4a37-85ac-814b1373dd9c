<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\Worker;
use App\Models\Contract;
use App\Models\Task;
use App\Models\Notification;
use App\ContractStatus;
use App\TaskStatus;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Users', User::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Active Users', User::where('is_active', true)->count())
                ->description('Currently active users')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('primary'),

            Stat::make('Verified Users', User::whereNotNull('email_verified_at')->whereNotNull('phone_verified_at')->count())
                ->description('Fully verified users')
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('success'),

            Stat::make('Total Workers', Worker::count())
                ->description('Worker positions')
                ->descriptionIcon('heroicon-m-briefcase')
                ->color('info'),

            Stat::make('Active Contracts', Contract::where('status_id', ContractStatus::Accepted)->count())
                ->description('Currently active contracts')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('success'),

            Stat::make('Pending Contracts', Contract::where('status_id', ContractStatus::Pending)->count())
                ->description('Awaiting approval')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Active Tasks', Task::where('is_active', true)->count())
                ->description('Currently active tasks')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('primary'),

            Stat::make('Completed Tasks', Task::where('status_id', TaskStatus::STATUS_COMPLETED)->count())
                ->description('Successfully completed')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('success'),

            Stat::make('Unread Notifications', Notification::whereNull('read_at')->count())
                ->description('Pending notifications')
                ->descriptionIcon('heroicon-m-bell')
                ->color('warning'),

            Stat::make('Total Balance', '₾ ' . number_format(User::sum('balance'), 2))
                ->description('Total user balances')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Network Balance', '₾ ' . number_format(Worker::sum('network_balance'), 2))
                ->description('Total network balances')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('info'),

            Stat::make('Average User Rating', number_format(User::whereNotNull('rate')->avg('rate'), 1) . '%')
                ->description('Average user rating')
                ->descriptionIcon('heroicon-m-star')
                ->color('primary'),
        ];
    }
}
