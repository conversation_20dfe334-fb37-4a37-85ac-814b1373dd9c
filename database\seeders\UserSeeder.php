<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+995555123456',
            'birth_date' => '1990-01-01',
            'gender' => 'male',
            'address' => 'Tbilisi, Georgia',
            'city' => 'Tbilisi',
            'country' => 'Georgia',
            'profile_completed' => true,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        // Create test users
        User::create([
            'name' => '<PERSON>',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+995555123457',
            'birth_date' => '1985-05-15',
            'gender' => 'male',
            'address' => 'Batumi, Georgia',
            'city' => 'Batumi',
            'country' => 'Georgia',
            'profile_completed' => true,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Jane Smith',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '+995555123458',
            'birth_date' => '1992-08-20',
            'gender' => 'female',
            'address' => 'Kutaisi, Georgia',
            'city' => 'Kutaisi',
            'country' => 'Georgia',
            'profile_completed' => true,
            'is_active' => true,
            'email_verified_at' => now(),
            'phone_verified_at' => now(),
        ]);

        echo "Test users created successfully!\n";
    }
}
