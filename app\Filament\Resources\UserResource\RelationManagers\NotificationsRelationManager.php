<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\NotificationStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class NotificationsRelationManager extends RelationManager
{
    protected static string $relationship = 'notifications';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Toggle::make('important')
                    ->default(false),
                Forms\Components\Select::make('status_id')
                    ->label('Type')
                    ->options([
                        NotificationStatus::INFO->value => 'Info',
                        NotificationStatus::WARNING->value => 'Warning',
                        NotificationStatus::DANGER->value => 'Danger',
                    ])
                    ->required()
                    ->default(NotificationStatus::INFO->value),
                Forms\Components\TextInput::make('rate')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(10),
                Forms\Components\Textarea::make('text')
                    ->required()
                    ->rows(4)
                    ->columnSpanFull(),
                Forms\Components\DateTimePicker::make('read_at')
                    ->label('Read At'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('text')
            ->columns([
                Tables\Columns\IconColumn::make('important')
                    ->boolean(),
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Type')
                    ->formatStateUsing(fn ($state) => match($state) {
                        NotificationStatus::INFO => 'Info',
                        NotificationStatus::WARNING => 'Warning',
                        NotificationStatus::DANGER => 'Danger',
                        default => 'Unknown'
                    })
                    ->colors([
                        'info' => NotificationStatus::INFO,
                        'warning' => NotificationStatus::WARNING,
                        'danger' => NotificationStatus::DANGER,
                    ]),
                Tables\Columns\TextColumn::make('text')
                    ->limit(50),
                Tables\Columns\IconColumn::make('read_at')
                    ->label('Read')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->read_at)),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\Filter::make('unread')
                    ->query(fn (Builder $query): Builder => $query->whereNull('read_at'))
                    ->toggle(),
                Tables\Filters\Filter::make('important')
                    ->query(fn (Builder $query): Builder => $query->where('important', true))
                    ->toggle(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('mark_as_read')
                    ->icon('heroicon-o-check')
                    ->action(fn ($record) => $record->markAsRead())
                    ->visible(fn ($record) => !$record->isRead()),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
