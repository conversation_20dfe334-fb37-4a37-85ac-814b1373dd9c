<?php

namespace App\Http\Requests\Node;

use Illuminate\Foundation\Http\FormRequest;

class AddRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'position' => 'required',
            'reward' => 'required',
//            'node_id' => 'required|exists:workers,id',
            'node_id' => [ // ამოწმებს იმას თუ მართლა თავის ნოუდს აბამს ქვემდგომს თუ არა და ქვემდგომების რაოდენობა 10ს მიაღწიი
                'required',
                function ($attribute, $value, $fail) {
                    $userId = auth()->id();

                    $parent = \App\Models\Worker::query()
                        ->where('id', $value)
                        ->where('user_id', $userId)
                        ->first();

                    if (!$parent) {
                        $fail('not_found');
                        return;
                    }

                    $childrenCount = $parent->children()->count();

                    if ($childrenCount >= 10) {
                        $fail('ten_limit_cross');
                    }
                },
            ],
        ];
    }
}
