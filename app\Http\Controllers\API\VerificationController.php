<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\EmailVerification;
use App\Models\PhoneVerification;
use App\Services\SMSService;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class VerificationController extends Controller
{
    protected $smsService;
    protected $emailService;

    public function __construct(SMSService $smsService, EmailService $emailService)
    {
        $this->smsService = $smsService;
        $this->emailService = $emailService;
    }

    /**
     * Send email verification
     */
    public function sendEmailVerification(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ]);

        try {
            $user = User::where('email', $request->email)->first();
            
            if ($user->email_verified_at) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is already verified'
                ], 400);
            }

            $verification = EmailVerification::createForEmail($request->email);
            
            // Send verification email
            $this->emailService->sendVerificationEmail($user, $verification);

            return response()->json([
                'success' => true,
                'message' => 'Verification email sent successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification email',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify email
     */
    public function verifyEmail(Request $request, $id, $hash): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            
            if ($user->email_verified_at) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email is already verified'
                ], 400);
            }

            $verification = EmailVerification::where('email', $user->email)
                ->where('token', $hash)
                ->active()
                ->first();

            if (!$verification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired verification link'
                ], 400);
            }

            // Mark email as verified
            $user->update(['email_verified_at' => Carbon::now()]);
            $verification->markAsVerified();

            return response()->json([
                'success' => true,
                'message' => 'Email verified successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Email verification failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send phone verification code
     */
    public function sendPhoneVerification(Request $request): JsonResponse
    {
        $request->validate([
            'phone' => 'required|string|exists:users,phone'
        ]);

        try {
            $user = User::where('phone', $request->phone)->first();
            
            if ($user->phone_verified_at) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phone is already verified'
                ], 400);
            }

            $verification = PhoneVerification::createForPhone($request->phone);
            
            // Send SMS
            $this->smsService->sendVerificationCode($request->phone, $verification->code);

            return response()->json([
                'success' => true,
                'message' => 'Verification code sent to your phone',
                'expires_at' => $verification->expires_at
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send verification code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify phone code
     */
    public function verifyPhone(Request $request): JsonResponse
    {
        $request->validate([
            'phone' => 'required|string',
            'code' => 'required|string|size:6'
        ]);

        try {
            $verification = PhoneVerification::where('phone', $request->phone)
                ->where('code', $request->code)
                ->active()
                ->first();

            if (!$verification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired verification code'
                ], 400);
            }

            if ($verification->maxAttemptsReached()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Maximum verification attempts reached. Please request a new code.'
                ], 400);
            }

            $verification->incrementAttempts();

            if ($verification->code !== $request->code) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid verification code',
                    'attempts_remaining' => 3 - $verification->attempts
                ], 400);
            }

            // Mark phone as verified
            $user = User::where('phone', $request->phone)->first();
            $user->update(['phone_verified_at' => Carbon::now()]);
            $verification->markAsVerified();

            return response()->json([
                'success' => true,
                'message' => 'Phone verified successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Phone verification failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
