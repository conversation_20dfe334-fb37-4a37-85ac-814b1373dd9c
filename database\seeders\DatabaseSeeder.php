<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(WorkerSeeder::class);

//        for ($i = 1; $i <= 10; $i++) {
//            User::query()->firstOrCreate(
//                ['id' => $i],
//                [
//                    'name' => "User $i",
//                    'email' => "user$<EMAIL>",
//                    'password' => bcrypt('password'), // use Hash::make() if needed
//                ]
//            );
//        }
    }
}
