<?php

namespace App\Filament\Resources\WorkerResource\RelationManagers;

use App\TaskStatus;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TasksRelationManager extends RelationManager
{
    protected static string $relationship = 'tasks';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status_id')
                    ->label('Status')
                    ->options([
                        TaskStatus::STATUS_STARTED->value => 'Started',
                        TaskStatus::STATUS_COMPLETED->value => 'Completed',
                        TaskStatus::STATUS_POSTPONED->value => 'Postponed',
                        TaskStatus::STATUS_FAILED->value => 'Failed',
                    ])
                    ->required()
                    ->default(TaskStatus::STATUS_STARTED->value),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
                Forms\Components\TextInput::make('rate')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(10),
                Forms\Components\Select::make('user_id')
                    ->label('Assigned User')
                    ->relationship('user', 'first_name')
                    ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                    ->searchable(['first_name', 'last_name', 'email'])
                    ->preload()
                    ->nullable(),
                Forms\Components\Select::make('type')
                    ->options([
                        0 => 'Recurring (Timelapse)',
                        1 => 'One-time (Perlapse)',
                    ])
                    ->required()
                    ->default(0),
                Forms\Components\TextInput::make('duration')
                    ->numeric()
                    ->required()
                    ->suffix('minutes'),
                Forms\Components\Textarea::make('task_description')
                    ->required()
                    ->rows(3)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('result_description')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('task_description')
            ->columns([
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        TaskStatus::STATUS_STARTED => 'Started',
                        TaskStatus::STATUS_COMPLETED => 'Completed',
                        TaskStatus::STATUS_POSTPONED => 'Postponed',
                        TaskStatus::STATUS_FAILED => 'Failed',
                        default => 'Unknown'
                    })
                    ->colors([
                        'info' => TaskStatus::STATUS_STARTED,
                        'success' => TaskStatus::STATUS_COMPLETED,
                        'warning' => TaskStatus::STATUS_POSTPONED,
                        'danger' => TaskStatus::STATUS_FAILED,
                    ]),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('rate')
                    ->suffix('/10'),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->formatStateUsing(fn ($record) => $record->user ? "{$record->user->first_name} {$record->user->last_name}" : 'Unassigned'),
                Tables\Columns\BadgeColumn::make('type')
                    ->formatStateUsing(fn ($state) => $state === 0 ? 'Recurring' : 'One-time')
                    ->colors([
                        'info' => 0,
                        'warning' => 1,
                    ]),
                Tables\Columns\TextColumn::make('duration')
                    ->suffix(' min'),
                Tables\Columns\TextColumn::make('task_description')
                    ->limit(30),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_id')
                    ->options([
                        TaskStatus::STATUS_STARTED->value => 'Started',
                        TaskStatus::STATUS_COMPLETED->value => 'Completed',
                        TaskStatus::STATUS_POSTPONED->value => 'Postponed',
                        TaskStatus::STATUS_FAILED->value => 'Failed',
                    ]),
                Tables\Filters\Filter::make('is_active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->toggle(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
