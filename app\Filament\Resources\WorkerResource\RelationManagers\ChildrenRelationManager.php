<?php

namespace App\Filament\Resources\WorkerResource\RelationManagers;

use App\Models\Worker;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChildrenRelationManager extends RelationManager
{
    protected static string $relationship = 'children';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('position')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                        if ($operation !== 'create') {
                            return;
                        }
                        $set('slug', \Illuminate\Support\Str::slug($state));
                    }),
                Forms\Components\TextInput::make('slug')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull()
                    ->rows(3),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name', fn ($query) => $query->whereNotNull('name'))
                    ->searchable()
                    ->preload()
                    ->nullable(),
                Forms\Components\TextInput::make('reward')
                    ->required()
                    ->numeric()
                    ->step(0.001)
                    ->suffix('%')
                    ->helperText('Reward percentage'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('position')
            ->columns([
                Tables\Columns\TextColumn::make('position')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Assigned User')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Unassigned')
                    ->badge()
                    ->color(fn ($state) => $state ? 'success' : 'warning'),
                Tables\Columns\TextColumn::make('reward')
                    ->label('Reward %')
                    ->numeric(decimalPlaces: 3)
                    ->sortable()
                    ->suffix('%')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('children_count')
                    ->label('Children')
                    ->counts('children')
                    ->badge()
                    ->color('info')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Assigned User')
                    ->relationship('user', 'name', fn ($query) => $query->whereNotNull('name'))
                    ->placeholder('All Users'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Set the parent_id to the current record
                        $data['parent_id'] = $this->ownerRecord->id;
                        return $data;
                    }),
                Action::make('attach_existing')
                    ->label('Attach Existing Worker')
                    ->icon('heroicon-o-link')
                    ->color('info')
                    ->form([
                        Forms\Components\Select::make('worker_id')
                            ->label('Select Worker')
                            ->options(function () {
                                return Worker::query()
                                    ->where('id', '!=', $this->ownerRecord->id)
                                    ->whereNotIn('id', $this->ownerRecord->descendants()->pluck('id'))
                                    ->whereNull('parent_id') // Only root level workers
                                    ->whereNotNull('position')
                                    ->orderBy('position')
                                    ->pluck('position', 'id');
                            })
                            ->searchable()
                            ->required()
                            ->helperText('Choose an existing worker to attach as child'),
                    ])
                    ->action(function (array $data) {
                        $worker = Worker::find($data['worker_id']);
                        if ($worker) {
                            $worker->appendToNode($this->ownerRecord)->save();
                            Notification::make()
                                ->title('Worker attached successfully')
                                ->success()
                                ->send();
                        }
                    })
                    ->visible(fn () => Worker::whereNull('parent_id')->where('id', '!=', $this->ownerRecord->id)->exists()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => \App\Filament\Resources\WorkerResource::getUrl('view', ['record' => $record])),
                Tables\Actions\EditAction::make(),
                Action::make('detach')
                    ->label('Detach')
                    ->icon('heroicon-o-x-mark')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Detach Worker')
                    ->modalDescription('Are you sure you want to detach this worker? It will be moved to root level.')
                    ->action(function ($record) {
                        $record->makeRoot()->save();
                        Notification::make()
                            ->title('Worker detached successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
