<?php

namespace App\Models;

use App\ContractStatus;
use App\TaskStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Task extends Model
{
    protected $table = 'tasks';

    protected $fillable = [
        'status_id',
        'is_active',
        'rate', // - ra x<PERSON>xze she<PERSON><PERSON><PERSON>

        'worker_id',
        'user_id',
//        'master_user_id',


        'type',
        'weekday',
        'hour',
        'minute',  // droebi
        'second',
        'started_at',
        'duration',


        'task_description',
        'task_file',
        'result_description',
        'result_file',
    ];

    protected $casts = [
        'status_id' => TaskStatus::class,
    ];

    public function worker(): BelongsTo
    {
        return $this->belongsTo(Worker::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

}
