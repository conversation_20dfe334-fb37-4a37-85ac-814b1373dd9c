<?php

namespace App\Filament\Resources\WorkerResource\Pages;

use App\Filament\Resources\WorkerResource;
use App\Models\Worker;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewWorker extends ViewRecord
{
    protected static string $resource = WorkerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Worker Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('position')
                            ->label('Position')
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('slug')
                            ->label('Slug')
                            ->badge()
                            ->color('gray'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                        Infolists\Components\TextEntry::make('reward')
                            ->label('Reward')
                            ->suffix('%')
                            ->numeric(decimalPlaces: 3),
                    ])->columns(2),

                Infolists\Components\Section::make('Hierarchy Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('parent.position')
                            ->label('Parent Worker')
                            ->placeholder('Root Level')
                            ->badge()
                            ->color('success'),
                        Infolists\Components\TextEntry::make('level')
                            ->label('Hierarchy Level')
                            ->getStateUsing(function ($record) {
                                if ($record->isRoot()) return 0;
                                return $record->ancestors()->count();
                            })
                            ->badge()
                            ->color(fn ($state) => match($state) {
                                0 => 'success',
                                1 => 'warning',
                                default => 'danger'
                            }),
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Assigned User')
                            ->placeholder('Unassigned')
                            ->badge()
                            ->color(fn ($state) => $state ? 'success' : 'warning'),
                    ])->columns(3),

                Infolists\Components\Section::make('Child Workers')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('children')
                            ->label('')
                            ->schema([
                                Infolists\Components\TextEntry::make('position')
                                    ->label('Position')
                                    ->weight('bold')
                                    ->url(fn ($record) => WorkerResource::getUrl('view', ['record' => $record]))
                                    ->color('primary'),
                                Infolists\Components\TextEntry::make('user.name')
                                    ->label('Assigned User')
                                    ->placeholder('Unassigned')
                                    ->badge()
                                    ->color(fn ($state) => $state ? 'success' : 'warning'),
                                Infolists\Components\TextEntry::make('reward')
                                    ->label('Reward')
                                    ->suffix('%')
                                    ->numeric(decimalPlaces: 3),
                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('view')
                                        ->label('View')
                                        ->icon('heroicon-o-eye')
                                        ->color('info')
                                        ->url(fn ($record) => WorkerResource::getUrl('view', ['record' => $record])),
                                    Infolists\Components\Actions\Action::make('edit')
                                        ->label('Edit')
                                        ->icon('heroicon-o-pencil')
                                        ->color('warning')
                                        ->url(fn ($record) => WorkerResource::getUrl('edit', ['record' => $record])),
                                ])
                                ->alignEnd(),
                            ])
                            ->columns(4)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => $record->children()->count() > 0)
                    ->headerActions([
                        Infolists\Components\Actions\Action::make('refresh')
                            ->label('Refresh')
                            ->icon('heroicon-o-arrow-path')
                            ->color('gray')
                            ->action(fn () => $this->refreshFormData(['children'])),
                    ]),

                Infolists\Components\Section::make('Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('children_count')
                            ->label('Direct Children')
                            ->getStateUsing(fn ($record) => $record->children()->count())
                            ->badge()
                            ->color('info'),
                        Infolists\Components\TextEntry::make('descendants_count')
                            ->label('All Descendants')
                            ->getStateUsing(fn ($record) => $record->descendants()->count())
                            ->badge()
                            ->color('primary'),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime(),
                    ])->columns(4),
            ]);
    }
}
