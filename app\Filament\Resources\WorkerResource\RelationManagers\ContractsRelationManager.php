<?php

namespace App\Filament\Resources\WorkerResource\RelationManagers;

use App\ContractStatus;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContractsRelationManager extends RelationManager
{
    protected static string $relationship = 'contracts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status_id')
                    ->label('Status')
                    ->options([
                        ContractStatus::Pending->value => 'Pending',
                        ContractStatus::Accepted->value => 'Accepted',
                        ContractStatus::Rejected->value => 'Rejected',
                        ContractStatus::Disqualified->value => 'Disqualified',
                        ContractStatus::LEAVE_REQUEST->value => 'Leave Request',
                    ])
                    ->required()
                    ->default(ContractStatus::Pending->value),
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'first_name')
                    ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                    ->searchable(['first_name', 'last_name', 'email'])
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('company_name')
                    ->maxLength(255),
                Forms\Components\Textarea::make('comment')
                    ->rows(3)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('user_comment')
                    ->rows(3)
                    ->columnSpanFull(),
                Forms\Components\DateTimePicker::make('end_date'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Contract ID'),
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        ContractStatus::Pending => 'Pending',
                        ContractStatus::Accepted => 'Accepted',
                        ContractStatus::Rejected => 'Rejected',
                        ContractStatus::Disqualified => 'Disqualified',
                        ContractStatus::LEAVE_REQUEST => 'Leave Request',
                        default => 'Unknown'
                    })
                    ->colors([
                        'warning' => ContractStatus::Pending,
                        'success' => ContractStatus::Accepted,
                        'danger' => ContractStatus::Rejected,
                        'gray' => ContractStatus::Disqualified,
                        'info' => ContractStatus::LEAVE_REQUEST,
                    ]),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->formatStateUsing(fn ($record) => "{$record->user->first_name} {$record->user->last_name}"),
                Tables\Columns\TextColumn::make('company_name'),
                Tables\Columns\TextColumn::make('end_date')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_id')
                    ->options([
                        ContractStatus::Pending->value => 'Pending',
                        ContractStatus::Accepted->value => 'Accepted',
                        ContractStatus::Rejected->value => 'Rejected',
                        ContractStatus::Disqualified->value => 'Disqualified',
                        ContractStatus::LEAVE_REQUEST->value => 'Leave Request',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
