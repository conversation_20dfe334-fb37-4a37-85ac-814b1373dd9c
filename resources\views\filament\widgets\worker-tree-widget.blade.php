<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            {{ $this->getHeading() }}
        </x-slot>

        <div class="space-y-4">
            @if($workers->count() > 0)
                @foreach($workers as $worker)
                    @include('filament.widgets.partials.worker-tree-node', ['worker' => $worker, 'level' => 0])
                @endforeach
            @else
                <div class="text-center py-8">
                    <div class="text-gray-400 text-sm">No workers found</div>
                </div>
            @endif
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
