<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationResource\Pages;
use App\Filament\Resources\NotificationResource\RelationManagers;
use App\Models\Notification;
use App\Models\User;
use App\NotificationStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class NotificationResource extends Resource
{
    protected static ?string $model = Notification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static ?string $navigationLabel = 'Notifications';

    protected static ?string $modelLabel = 'Notification';

    protected static ?string $pluralModelLabel = 'Notifications';

    protected static ?string $navigationGroup = 'Communication';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Notification Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('User')
                            ->relationship('user', 'first_name')
                            ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->preload()
                            ->required(),
                        Forms\Components\Toggle::make('important')
                            ->label('Important')
                            ->default(false)
                            ->helperText('Mark as important notification'),
                        Forms\Components\Select::make('status_id')
                            ->label('Status Type')
                            ->options([
                                NotificationStatus::INFO->value => 'Info',
                                NotificationStatus::WARNING->value => 'Warning',
                                NotificationStatus::DANGER->value => 'Danger',
                            ])
                            ->required()
                            ->default(NotificationStatus::INFO->value),
                        Forms\Components\TextInput::make('rate')
                            ->label('Rate')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(10)
                            ->helperText('Optional rating (0-10)'),
                    ])->columns(2),

                Forms\Components\Section::make('Message')
                    ->schema([
                        Forms\Components\Textarea::make('text')
                            ->label('Notification Text')
                            ->required()
                            ->rows(4)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Read Status')
                    ->schema([
                        Forms\Components\DateTimePicker::make('read_at')
                            ->label('Read At')
                            ->helperText('Leave empty for unread notifications'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->formatStateUsing(fn ($record) => "{$record->user->first_name} {$record->user->last_name}")
                    ->searchable(['user.first_name', 'user.last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('important')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Type')
                    ->formatStateUsing(fn ($state) => match($state) {
                        NotificationStatus::INFO => 'Info',
                        NotificationStatus::WARNING => 'Warning',
                        NotificationStatus::DANGER => 'Danger',
                        default => 'Unknown'
                    })
                    ->colors([
                        'info' => NotificationStatus::INFO,
                        'warning' => NotificationStatus::WARNING,
                        'danger' => NotificationStatus::DANGER,
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('rate')
                    ->numeric()
                    ->sortable()
                    ->suffix('/10')
                    ->alignEnd()
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('text')
                    ->label('Message')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\IconColumn::make('read_at')
                    ->label('Read')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->read_at))
                    ->sortable(),
                Tables\Columns\TextColumn::make('read_at')
                    ->label('Read At')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Unread')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status_id')
                    ->label('Type')
                    ->options([
                        NotificationStatus::INFO->value => 'Info',
                        NotificationStatus::WARNING->value => 'Warning',
                        NotificationStatus::DANGER->value => 'Danger',
                    ]),
                Filter::make('important')
                    ->label('Important Only')
                    ->query(fn (Builder $query): Builder => $query->where('important', true))
                    ->toggle(),
                Filter::make('unread')
                    ->label('Unread Only')
                    ->query(fn (Builder $query): Builder => $query->whereNull('read_at'))
                    ->toggle(),
                Filter::make('read')
                    ->label('Read Only')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('read_at'))
                    ->toggle(),
                SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'first_name')
                    ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name}")
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\Action::make('mark_as_read')
                    ->label('Mark as Read')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(fn (Notification $record) => $record->markAsRead())
                    ->visible(fn (Notification $record) => !$record->isRead()),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_as_read')
                        ->label('Mark as Read')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(fn ($records) => $records->each->markAsRead()),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotifications::route('/'),
            'create' => Pages\CreateNotification::route('/create'),
            'view' => Pages\ViewNotification::route('/{record}'),
            'edit' => Pages\EditNotification::route('/{record}/edit'),
        ];
    }
}
