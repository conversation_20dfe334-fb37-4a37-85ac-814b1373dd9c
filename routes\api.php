<?php

use App\Events\JobAccepted;
use App\Http\Controllers\NetworkController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\VerificationController;
use App\Http\Controllers\API\PasswordResetController;
use App\Http\Controllers\API\NotificationController;
use App\Http\Controllers\TaskController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Broadcast::routes();

Route::get('te', function ()
{
    event(new \App\Events\NotificationEvent(
        1,
        true,
        2,
        null,
        'test'
    ));
});

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword']);
Route::post('/reset-password', [PasswordResetController::class, 'resetPassword']);

// Email verification
Route::post('/email/verify/send', [VerificationController::class, 'sendEmailVerification']);
Route::post('/email/verify/{id}/{hash}', [VerificationController::class, 'verifyEmail'])->name('verification.verify');

// Phone verification
Route::post('/phone/verify/send', [VerificationController::class, 'sendPhoneVerification']);
Route::post('/phone/verify', [VerificationController::class, 'verifyPhone']);

// Protected routes
Route::middleware([
//    'auth:sanctum'
])->group(function () {
    // User profile
    Route::get('/user', [UserController::class, 'show']);
    Route::put('/user', [UserController::class, 'update']);
    Route::post('/user/video', [UserController::class, 'uploadVideo']);

    // Logout
    Route::post('/logout', [AuthController::class, 'logout']);

    // Notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
//    Route::patch('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/read', [NotificationController::class, 'markAsRead']);
    Route::patch('/notifications/read-all', [NotificationController::class, 'markAllAsRead']);
});



Route::middleware([
//    'auth:sanctum'
])->group(function () {
    Route::get('network', [NetworkController::class, 'network']);
    Route::post('add-node', [NetworkController::class, 'addNode']);
    Route::post('node/apply', [NetworkController::class, 'applyNode']); // ორი შემოწმება რექვესტ ვალიდაციაში, რო იერარქიაშ არ არსებობდეს და მეორე ისრო საერთოდ დარეჯექთებული იყოს ამ იერეარქიაშინ
    Route::get('applicants/{id?}', [NetworkController::class, 'applicants']);
    Route::get('applies', [NetworkController::class, 'applies']);
    Route::post('node/applicant/confirm', [NetworkController::class, 'confirmApplicant']);
    Route::post('fired', [NetworkController::class, 'fired']);











    Route::get('node/{id}/tasks', [TaskController::class, 'nodeTasks']);
    Route::post('tasks', [TaskController::class, 'storeTask'])->middleware('worker.visibility');

    // taskis washlashi unda shemowmdes ubralod shli tu vinmes gagdeba ginda.. tu gagdeba ginda mashin unda gavardes

});
