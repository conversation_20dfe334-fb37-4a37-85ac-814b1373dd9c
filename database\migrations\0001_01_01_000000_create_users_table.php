<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();

            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->date('birth_date')->nullable()->after('last_name');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('birth_date');
            $table->string('phone')->nullable()->unique()->after('gender');
            $table->string('address')->nullable()->after('phone');
            $table->string('city')->nullable()->after('address');
            $table->string('country')->nullable()->after('city');

            $table->decimal('balance', 15, 4)->default(0);
            $table->unsignedInteger('rate')->nullable()->comment('in %');
            $table->unsignedBigInteger('leave_count')->index()->default(0);
            $table->unsignedBigInteger('reject_count')->default(0);

            // Verification fields
            $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            $table->boolean('profile_completed')->default(false)->after('phone_verified_at');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();

            // Video verification
            $table->string('verification_video_path')->nullable()->after('profile_completed');
            $table->timestamp('video_verified_at')->nullable()->after('verification_video_path');
            $table->enum('video_status', ['pending', 'approved', 'rejected'])->default('pending')->after('video_verified_at');
            $table->text('video_rejection_reason')->nullable()->after('video_status');

            // Account status
            $table->boolean('is_active')->default(true)->after('video_rejection_reason');
            $table->timestamp('last_login_at')->nullable()->after('is_active');

            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
