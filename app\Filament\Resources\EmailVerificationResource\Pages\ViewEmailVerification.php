<?php

namespace App\Filament\Resources\EmailVerificationResource\Pages;

use App\Filament\Resources\EmailVerificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewEmailVerification extends ViewRecord
{
    protected static string $resource = EmailVerificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
