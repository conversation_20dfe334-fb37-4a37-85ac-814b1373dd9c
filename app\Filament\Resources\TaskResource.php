<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaskResource\Pages;
use App\Filament\Resources\TaskResource\RelationManagers;
use App\Models\Task;
use App\Models\User;
use App\Models\Worker;
use App\TaskStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationLabel = 'Tasks';

    protected static ?string $modelLabel = 'Task';

    protected static ?string $pluralModelLabel = 'Tasks';

    protected static ?string $navigationGroup = 'Work Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Task Information')
                    ->schema([
                        Forms\Components\Select::make('status_id')
                            ->label('Status')
                            ->options([
                                TaskStatus::STATUS_STARTED->value => 'Started',
                                TaskStatus::STATUS_COMPLETED->value => 'Completed',
                                TaskStatus::STATUS_POSTPONED->value => 'Postponed',
                                TaskStatus::STATUS_FAILED->value => 'Failed',
                            ])
                            ->required()
                            ->default(TaskStatus::STATUS_STARTED->value),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        Forms\Components\TextInput::make('rate')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(10)
                            ->helperText('Task completion rate (0-10)'),
                        Forms\Components\Select::make('worker_id')
                            ->label('Worker Position')
                            ->relationship('worker', 'position')
                            ->searchable(['position'])
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('user_id')
                            ->label('Assigned User')
                            ->relationship('user', 'first_name')
                            ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->preload()
                            ->nullable(),
                    ])->columns(2),

                Forms\Components\Section::make('Task Scheduling')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->options([
                                0 => 'Recurring (Timelapse)',
                                1 => 'One-time (Perlapse)',
                            ])
                            ->required()
                            ->default(0)
                            ->live(),
                        Forms\Components\DateTimePicker::make('started_at')
                            ->label('Start Time'),
                        Forms\Components\TextInput::make('duration')
                            ->numeric()
                            ->required()
                            ->suffix('minutes')
                            ->helperText('Task duration in minutes'),
                    ])->columns(3),

                Forms\Components\Section::make('Recurring Schedule')
                    ->schema([
                        Forms\Components\Select::make('weekday')
                            ->options([
                                0 => 'Sunday',
                                1 => 'Monday',
                                2 => 'Tuesday',
                                3 => 'Wednesday',
                                4 => 'Thursday',
                                5 => 'Friday',
                                6 => 'Saturday',
                            ])
                            ->helperText('Day of the week (for recurring tasks)'),
                        Forms\Components\TextInput::make('hour')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(23)
                            ->helperText('Hour (0-23)'),
                        Forms\Components\TextInput::make('minute')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(59)
                            ->helperText('Minute (0-59)'),
                        Forms\Components\TextInput::make('second')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(59)
                            ->default(0)
                            ->helperText('Second (0-59)'),
                    ])
                    ->columns(4)
                    ->visible(fn (Forms\Get $get) => $get('type') === 0),

                Forms\Components\Section::make('Task Details')
                    ->schema([
                        Forms\Components\Textarea::make('task_description')
                            ->required()
                            ->rows(4)
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('task_file')
                            ->label('Task File')
                            ->directory('task_files')
                            ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx', '.txt'])
                            ->maxSize(10240), // 10MB
                    ]),

                Forms\Components\Section::make('Task Results')
                    ->schema([
                        Forms\Components\Textarea::make('result_description')
                            ->rows(4)
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('result_file')
                            ->label('Result File')
                            ->directory('result_files')
                            ->acceptedFileTypes(['image/*', 'application/pdf', '.doc', '.docx', '.txt'])
                            ->maxSize(10240), // 10MB
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        TaskStatus::STATUS_STARTED => 'Started',
                        TaskStatus::STATUS_COMPLETED => 'Completed',
                        TaskStatus::STATUS_POSTPONED => 'Postponed',
                        TaskStatus::STATUS_FAILED => 'Failed',
                        default => 'Unknown'
                    })
                    ->colors([
                        'info' => TaskStatus::STATUS_STARTED,
                        'success' => TaskStatus::STATUS_COMPLETED,
                        'warning' => TaskStatus::STATUS_POSTPONED,
                        'danger' => TaskStatus::STATUS_FAILED,
                    ])
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rate')
                    ->numeric()
                    ->sortable()
                    ->suffix('/10')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('worker.position')
                    ->label('Position')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->formatStateUsing(fn ($record) => $record->user ? "{$record->user->first_name} {$record->user->last_name}" : 'Unassigned')
                    ->searchable(['user.first_name', 'user.last_name'])
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->formatStateUsing(fn ($state) => $state === 0 ? 'Recurring' : 'One-time')
                    ->colors([
                        'info' => 0,
                        'warning' => 1,
                    ]),
                Tables\Columns\TextColumn::make('duration')
                    ->suffix(' min')
                    ->sortable(),
                Tables\Columns\TextColumn::make('task_description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status_id')
                    ->label('Status')
                    ->options([
                        TaskStatus::STATUS_STARTED->value => 'Started',
                        TaskStatus::STATUS_COMPLETED->value => 'Completed',
                        TaskStatus::STATUS_POSTPONED->value => 'Postponed',
                        TaskStatus::STATUS_FAILED->value => 'Failed',
                    ]),
                SelectFilter::make('type')
                    ->options([
                        0 => 'Recurring',
                        1 => 'One-time',
                    ]),
                SelectFilter::make('worker_id')
                    ->label('Worker Position')
                    ->relationship('worker', 'position')
                    ->searchable()
                    ->preload(),
                Filter::make('is_active')
                    ->label('Active Tasks')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->toggle(),
                Filter::make('has_user')
                    ->label('Assigned Tasks')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('user_id'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTasks::route('/'),
            'create' => Pages\CreateTask::route('/create'),
            'view' => Pages\ViewTask::route('/{record}'),
            'edit' => Pages\EditTask::route('/{record}/edit'),
        ];
    }
}
