<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class NotificationController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->query('per_page', 10);
        $user = $request->user();

        return response()->json([
            'success' => true,
            'unreadCount' => $user->notifications()->whereNull('read_at')->count(),
            'data' => $user
                ->notifications()
                ->orderByDesc('id')
                ->paginate($perPage)
        ]);
    }

    public function markAsRead(Request $request)
    {
        Notification::query()
            ->where('user_id', auth()->id())
                ->whereIn('id', $request->notification_ids)
            ->update(['read_at' => now()])
        ;

        return response()->json([
            'success' => true,
            'message' => 'notification_marked_as_read'
        ]);
    }

    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = $request->user();

        $user->notifications()
            ->unread()
            ->update(['read_at' => now()]);


        return response()->json([
            'success' => true,
            'message' => 'all_notifications_marked_as_read'
        ]);
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        $notification = $user->notifications()->findOrFail($id);
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'notification_deleted'
        ]);
    }
}
