<?php

namespace App\Filament\Resources\ContractPointResource\Pages;

use App\Filament\Resources\ContractPointResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewContractPoint extends ViewRecord
{
    protected static string $resource = ContractPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
