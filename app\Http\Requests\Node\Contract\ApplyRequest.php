<?php

namespace App\Http\Requests\Node\Contract;

use App\ContractStatus;
use App\Models\Contract;
use App\Models\Worker;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ApplyRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'node_id' => [
                'required',
                'exists:workers,id',
                function ($attribute, $value, $fail) {
            // თუ არჩეული ნოუდის იერარქიაშია სადმე ამწუთას
                    $onActivePosition = Contract::query()
                        ->where('user_id', auth()->id())
                        ->where('worker_id', $value)
                        ->select(['id', 'status_id', 'worker_id', 'end_date', 'user_id'])
                        ->get()
                    ;

                    if($onActivePosition->contains('status_id', ContractStatus::Pending))
                    {
                        $fail('you_already_apply_this_network_node');
                        return;
                    }

                    if($onActivePosition->contains('end_date', null)){
                        $fail('you_are_already_own_this_networks_node');
                    }

                    if($onActivePosition->contains('status_id', ContractStatus::Rejected))
                    {
                        $fail('you_are_rejected_from_this_network');
                        return;
                    }

                    // თუ ამ იერარქიიდან ადრე დარეჯექდა
                },
            ],
        ];
    }

}
