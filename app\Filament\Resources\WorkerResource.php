<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WorkerResource\Pages;
use App\Filament\Resources\WorkerResource\RelationManagers;
use App\Models\Worker;
use App\Models\User;
use App\WorkerStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WorkerResource extends Resource
{
    protected static ?string $model = Worker::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Workers';

    protected static ?string $modelLabel = 'Worker';

    protected static ?string $pluralModelLabel = 'Workers';

    protected static ?string $navigationGroup = 'Work Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('position')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                if ($operation !== 'create') {
                                    return;
                                }
                                $set('slug', \Illuminate\Support\Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull()
                            ->rows(3),
                        Forms\Components\TextInput::make('reward')
                            ->required()
                            ->numeric()
                            ->step(0.001)
                            ->suffix('%')
                            ->helperText('Reward percentage (e.g., 5.250 for 5.25%)'),
                    ])->columns(2),

                Forms\Components\Section::make('Status & Balance')
                    ->schema([
                        Forms\Components\Select::make('pointer_status_id')
                            ->label('Status')
                            ->options([
                                WorkerStatus::ACTIVE_WORK->value => 'Active Work',
                                WorkerStatus::INEFFECTIVE->value => 'Ineffective',
                                WorkerStatus::VACANT->value => 'Vacant',
                            ])
                            ->required()
                            ->default(WorkerStatus::ACTIVE_WORK->value),
                        Forms\Components\TextInput::make('network_balance')
                            ->label('Network Balance')
                            ->numeric()
                            ->step(0.0001)
                            ->default(0)
                            ->suffix('₾')
                            ->helperText('Current network balance'),
                    ])->columns(2),

                Forms\Components\Section::make('Hierarchy')
                    ->schema([
                        Forms\Components\Select::make('current_user_id')
                            ->label('Assigned User')
                            ->options(function () {
                                return User::query()
                                    ->whereNotNull('first_name')
                                    ->orderBy('first_name')
                                    ->get()
                                    ->mapWithKeys(fn ($user) => [$user->id => "{$user->first_name} {$user->last_name} ({$user->email})"]);
                            })
                            ->searchable()
                            ->nullable()
                            ->helperText('Assign a user to this worker position (this will create/update contract)'),
                        Forms\Components\Select::make('parent_id')
                            ->label('Parent Worker')
                            ->options(function () {
                                return Worker::query()
                                    ->whereNotNull('position')
                                    ->orderBy('position')
                                    ->pluck('position', 'id');
                            })
                            ->searchable()
                            ->nullable()
                            ->helperText('Select parent worker in hierarchy'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('position')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('parent.position')
                    ->label('Parent')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Root Level')
                    ->badge()
                    ->color('gray'),
                Tables\Columns\TextColumn::make('assigned_user')
                    ->label('Assigned User')
                    ->getStateUsing(function ($record) {
                        $user = $record->activeUser();
                        return $user ? "{$user->first_name} {$user->last_name}" : 'Unassigned';
                    })
                    ->badge()
                    ->color(fn ($record) => $record->activeUser() ? 'success' : 'warning'),
                Tables\Columns\BadgeColumn::make('pointer_status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        WorkerStatus::ACTIVE_WORK => 'Active Work',
                        WorkerStatus::INEFFECTIVE => 'Ineffective',
                        WorkerStatus::VACANT => 'Vacant',
                        default => 'Unknown'
                    })
                    ->colors([
                        'success' => WorkerStatus::ACTIVE_WORK,
                        'warning' => WorkerStatus::INEFFECTIVE,
                        'danger' => WorkerStatus::VACANT,
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('reward')
                    ->label('Reward %')
                    ->numeric(decimalPlaces: 3)
                    ->sortable()
                    ->suffix('%')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('network_balance')
                    ->label('Network Balance')
                    ->numeric(decimalPlaces: 4)
                    ->sortable()
                    ->suffix(' ₾')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('children_count')
                    ->label('Children')
                    ->counts('children')
                    ->badge()
                    ->color('info')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('level')
                    ->label('Level')
                    ->getStateUsing(function ($record) {
                        if ($record->isRoot()) return 0;
                        return $record->ancestors()->count();
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        0 => 'success',
                        1 => 'warning',
                        default => 'danger'
                    })
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('_lft', 'asc')
            ->filters([
                SelectFilter::make('pointer_status_id')
                    ->label('Status')
                    ->options([
                        WorkerStatus::ACTIVE_WORK->value => 'Active Work',
                        WorkerStatus::INEFFECTIVE->value => 'Ineffective',
                        WorkerStatus::VACANT->value => 'Vacant',
                    ]),
                SelectFilter::make('parent_id')
                    ->label('Parent Worker')
                    ->options(function () {
                        return Worker::query()
                            ->whereNotNull('position')
                            ->orderBy('position')
                            ->pluck('position', 'id');
                    })
                    ->placeholder('All Workers'),
                SelectFilter::make('has_user')
                    ->label('Assignment Status')
                    ->options([
                        'assigned' => 'Assigned',
                        'unassigned' => 'Unassigned',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['value'] === 'assigned') {
                            return $query->whereHas('contracts', function ($q) {
                                $q->where('status_id', \App\ContractStatus::Accepted);
                            });
                        } elseif ($data['value'] === 'unassigned') {
                            return $query->whereDoesntHave('contracts', function ($q) {
                                $q->where('status_id', \App\ContractStatus::Accepted);
                            });
                        }
                        return $query;
                    }),
                Tables\Filters\Filter::make('is_root')
                    ->label('Root Level Only')
                    ->query(fn (Builder $query): Builder => $query->whereIsRoot())
                    ->toggle(),
                Tables\Filters\Filter::make('has_children')
                    ->label('Has Children')
                    ->query(fn (Builder $query): Builder => $query->has('children'))
                    ->toggle(),
            ])
            ->actions([
                Action::make('create_child')
                    ->label('Create Child Worker')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->form([
                        Forms\Components\TextInput::make('position')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                if ($operation !== 'create') {
                                    return;
                                }
                                $set('slug', \Illuminate\Support\Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->rows(3),
                        Forms\Components\Select::make('assigned_user_id')
                            ->label('Assigned User')
                            ->options(function () {
                                return User::query()
                                    ->whereNotNull('first_name')
                                    ->orderBy('first_name')
                                    ->get()
                                    ->mapWithKeys(fn ($user) => [$user->id => "{$user->first_name} {$user->last_name} ({$user->email})"]);
                            })
                            ->searchable()
                            ->nullable(),
                        Forms\Components\TextInput::make('reward')
                            ->required()
                            ->numeric()
                            ->step(0.001)
                            ->suffix('%')
                            ->helperText('Reward percentage'),
                    ])
                    ->action(function (Worker $record, array $data) {
                        $child = Worker::create($data);
                        $child->appendToNode($record)->save();
                        Notification::make()
                            ->title('Child worker created successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ChildrenRelationManager::class,
            RelationManagers\TasksRelationManager::class,
            RelationManagers\ContractsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWorkers::route('/'),
            'create' => Pages\CreateWorker::route('/create'),
            'view' => Pages\ViewWorker::route('/{record}'),
            'edit' => Pages\EditWorker::route('/{record}/edit'),
        ];
    }
}
