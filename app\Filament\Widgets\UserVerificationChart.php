<?php

namespace App\Filament\Widgets;

use App\Models\User;
use Filament\Widgets\ChartWidget;

class UserVerificationChart extends ChartWidget
{
    protected static ?string $heading = 'User Verification Status';

    protected function getData(): array
    {
        $totalUsers = User::count();
        $emailVerified = User::whereNotNull('email_verified_at')->count();
        $phoneVerified = User::whereNotNull('phone_verified_at')->count();
        $videoVerified = User::whereNotNull('video_verified_at')->count();
        $fullyVerified = User::whereNotNull('email_verified_at')
            ->whereNotNull('phone_verified_at')
            ->whereNotNull('video_verified_at')
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'Verification Status',
                    'data' => [
                        $totalUsers - $emailVerified, // Unverified Email
                        $emailVerified - $phoneVerified, // Email Only
                        $phoneVerified - $videoVerified, // Email + Phone
                        $videoVerified - $fullyVerified, // Email + Phone + Video (pending)
                        $fullyVerified, // Fully Verified
                    ],
                    'backgroundColor' => [
                        '#ef4444', // red
                        '#f97316', // orange
                        '#eab308', // yellow
                        '#3b82f6', // blue
                        '#22c55e', // green
                    ],
                ],
            ],
            'labels' => [
                'Unverified',
                'Email Only',
                'Email + Phone',
                'Pending Video',
                'Fully Verified'
            ],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
