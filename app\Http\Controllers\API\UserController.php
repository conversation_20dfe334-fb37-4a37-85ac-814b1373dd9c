<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * Get authenticated user profile
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user->makeHidden(['password']),
                    'profile_completed' => $user->hasCompletedProfile(),
                    'fully_verified' => $user->isFullyVerified(),
                    'verification_status' => [
                        'email_verified' => !is_null($user->email_verified_at),
                        'phone_verified' => !is_null($user->phone_verified_at),
                        'video_status' => $user->video_status,
                        'video_verified' => !is_null($user->video_verified_at),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch user profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user profile
     */
    public function update(ProfileUpdateRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            $updateData = $request->validated();
            
            // Remove password from update data if not provided
            if (empty($updateData['password'])) {
                unset($updateData['password']);
            } else {
                $updateData['password'] = bcrypt($updateData['password']);
            }

            $user->update($updateData);

            // Check if profile is now completed
            $profileCompleted = $user->hasCompletedProfile();
            if ($profileCompleted && !$user->profile_completed) {
                $user->update(['profile_completed' => true]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => $user->fresh()->makeHidden(['password']),
                    'profile_completed' => $profileCompleted,
                    'fully_verified' => $user->isFullyVerified(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload verification video
     */
    public function uploadVideo(Request $request): JsonResponse
    {
        $request->validate([
            'video' => 'required|file|mimes:mp4,mov,avi,wmv|max:102400', // 100MB max
        ]);

        try {
            $user = $request->user();
            
            if ($request->hasFile('video')) {
                $video = $request->file('video');
                
                // Validate video duration (1-2 minutes)
                // Note: This would require FFmpeg or similar to check duration
                // For now, we'll just validate file size as a proxy
                
                // Generate unique filename
                $filename = 'verification_videos/' . $user->id . '_' . Str::random(10) . '.' . $video->getClientOriginalExtension();
                
                // Store the video
                $path = $video->storeAs('public', $filename);
                
                // Delete old video if exists
                if ($user->verification_video_path) {
                    Storage::delete('public/' . $user->verification_video_path);
                }
                
                // Update user record
                $user->update([
                    'verification_video_path' => $filename,
                    'video_status' => 'pending',
                    'video_verified_at' => null,
                    'video_rejection_reason' => null,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Video uploaded successfully and is pending review',
                    'data' => [
                        'video_path' => $filename,
                        'video_status' => 'pending',
                        'profile_completed' => $user->hasCompletedProfile(),
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No video file provided'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload video',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
