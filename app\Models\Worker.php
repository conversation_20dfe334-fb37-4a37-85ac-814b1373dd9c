<?php

namespace App\Models;

use App\WorkerStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Kalnoy\Nestedset\NodeTrait;

class Worker extends Model
{
    use NodeTrait;

    protected $table = 'workers';

    protected $fillable = [
        'pointer_status_id',
        'contract_id',
        'position',
        'slug',
        'description',
        'reward',
        'network_balance',

    ];

    protected $casts = [
        'status_id' => WorkerStatus::class,
    ];

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    // current contract
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id', 'id');
    }

    // all contract history
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

}
