<?php

namespace App\Filament\Resources\ContractResource\RelationManagers;

use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PointsRelationManager extends RelationManager
{
    protected static string $relationship = 'points';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Evaluator User')
                    ->relationship('user', 'first_name')
                    ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                    ->searchable(['first_name', 'last_name', 'email'])
                    ->preload()
                    ->nullable(),
                Forms\Components\TextInput::make('point')
                    ->label('Rating Points')
                    ->numeric()
                    ->required()
                    ->minValue(0)
                    ->maxValue(10)
                    ->step(1),
                Forms\Components\TextInput::make('count')
                    ->label('Count')
                    ->numeric()
                    ->required()
                    ->default(0)
                    ->minValue(0),
                Forms\Components\Textarea::make('comment')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('point')
            ->columns([
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('Evaluator')
                    ->formatStateUsing(fn ($record) => $record->user ? "{$record->user->first_name} {$record->user->last_name}" : 'System'),
                Tables\Columns\BadgeColumn::make('point')
                    ->label('Rating')
                    ->colors([
                        'danger' => fn ($state) => $state <= 3,
                        'warning' => fn ($state) => $state > 3 && $state <= 6,
                        'success' => fn ($state) => $state > 6 && $state <= 8,
                        'primary' => fn ($state) => $state > 8,
                    ])
                    ->formatStateUsing(fn ($state) => $state . '/10'),
                Tables\Columns\TextColumn::make('count')
                    ->numeric(),
                Tables\Columns\TextColumn::make('comment')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('point')
                    ->options([
                        0 => '0/10',
                        1 => '1/10',
                        2 => '2/10',
                        3 => '3/10',
                        4 => '4/10',
                        5 => '5/10',
                        6 => '6/10',
                        7 => '7/10',
                        8 => '8/10',
                        9 => '9/10',
                        10 => '10/10',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
