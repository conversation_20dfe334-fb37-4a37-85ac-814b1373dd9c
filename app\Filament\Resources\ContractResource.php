<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractResource\Pages;
use App\Filament\Resources\ContractResource\RelationManagers;
use App\Models\Contract;
use App\Models\User;
use App\Models\Worker;
use App\ContractStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContractResource extends Resource
{
    protected static ?string $model = Contract::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Contracts';

    protected static ?string $modelLabel = 'Contract';

    protected static ?string $pluralModelLabel = 'Contracts';

    protected static ?string $navigationGroup = 'Work Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Contract Information')
                    ->schema([
                        Forms\Components\Select::make('status_id')
                            ->label('Status')
                            ->options([
                                ContractStatus::Pending->value => 'Pending',
                                ContractStatus::Accepted->value => 'Accepted',
                                ContractStatus::Rejected->value => 'Rejected',
                                ContractStatus::Disqualified->value => 'Disqualified',
                                ContractStatus::LEAVE_REQUEST->value => 'Leave Request',
                            ])
                            ->required()
                            ->default(ContractStatus::Pending->value),
                        Forms\Components\Select::make('user_id')
                            ->label('User')
                            ->relationship('user', 'first_name')
                            ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('worker_id')
                            ->label('Worker Position')
                            ->relationship('worker', 'position')
                            ->searchable(['position'])
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('company_name')
                            ->maxLength(255)
                            ->helperText('Optional company name'),
                    ])->columns(2),

                Forms\Components\Section::make('Comments & Details')
                    ->schema([
                        Forms\Components\Textarea::make('comment')
                            ->label('Admin Comment')
                            ->rows(3)
                            ->helperText('Reason for rejection or other admin notes')
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('user_comment')
                            ->label('User Comment')
                            ->rows(3)
                            ->helperText('User reason for leaving or other notes')
                            ->columnSpanFull(),
                        Forms\Components\DateTimePicker::make('end_date')
                            ->label('End Date')
                            ->helperText('Contract end date if applicable'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status_id')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => match($state) {
                        ContractStatus::Pending => 'Pending',
                        ContractStatus::Accepted => 'Accepted',
                        ContractStatus::Rejected => 'Rejected',
                        ContractStatus::Disqualified => 'Disqualified',
                        ContractStatus::LEAVE_REQUEST => 'Leave Request',
                        default => 'Unknown'
                    })
                    ->colors([
                        'warning' => ContractStatus::Pending,
                        'success' => ContractStatus::Accepted,
                        'danger' => ContractStatus::Rejected,
                        'gray' => ContractStatus::Disqualified,
                        'info' => ContractStatus::LEAVE_REQUEST,
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('User')
                    ->formatStateUsing(fn ($record) => "{$record->user->first_name} {$record->user->last_name}")
                    ->searchable(['user.first_name', 'user.last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('worker.position')
                    ->label('Position')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('company_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('end_date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status_id')
                    ->label('Status')
                    ->options([
                        ContractStatus::Pending->value => 'Pending',
                        ContractStatus::Accepted->value => 'Accepted',
                        ContractStatus::Rejected->value => 'Rejected',
                        ContractStatus::Disqualified->value => 'Disqualified',
                        ContractStatus::LEAVE_REQUEST->value => 'Leave Request',
                    ]),
                SelectFilter::make('worker_id')
                    ->label('Worker Position')
                    ->relationship('worker', 'position')
                    ->searchable()
                    ->preload(),
                Filter::make('has_end_date')
                    ->label('Has End Date')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('end_date'))
                    ->toggle(),
                Filter::make('active_contracts')
                    ->label('Active Contracts')
                    ->query(fn (Builder $query): Builder => $query->where('status_id', ContractStatus::Accepted))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PointsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContracts::route('/'),
            'create' => Pages\CreateContract::route('/create'),
            'view' => Pages\ViewContract::route('/{record}'),
            'edit' => Pages\EditContract::route('/{record}/edit'),
        ];
    }
}
