<?php

namespace App\Events;

use App\Models\Notification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public $userId, public $important, public $statusId, public $rate, public $text)
    {
        Notification::query()->create([
            'user_id' => $this->userId,
            'important' => $this->important,
            'status_id' => $this->statusId,
            'rate' => $this->rate,
            'text' => $this->text,
        ]);
    }

    public function broadcastOn(): array
    {
        return [
            new Channel('notification.user-'. $this->userId),
        ];
    }

    public function broadcastAs(): string
    {
        return 'GeneralNotification';
    }

    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->userId,
            'important' => $this->important,
            'status_id' => $this->statusId,
            'rate' => $this->rate,
            'text' => $this->text,
        ];
    }
}
