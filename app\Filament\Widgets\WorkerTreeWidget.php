<?php

namespace App\Filament\Widgets;

use App\Models\Worker;
use Filament\Widgets\Widget;

class WorkerTreeWidget extends Widget
{
    protected static string $view = 'filament.widgets.worker-tree-widget';
    
    protected static ?int $sort = 1;
    
    protected static ?string $heading = 'Worker Hierarchy Tree';
    
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        return [
            'workers' => Worker::with(['children', 'user'])->whereIsRoot()->get()->toTree(),
        ];
    }
}
