<?php

namespace App\Console\Commands;

use App\Models\Worker;
use Illuminate\Console\Command;

class SalleryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'te';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $workers = Worker::query()
            ->whereNull('parent_id')
            ->orderBy('id')
            ->get()
            ->map(function ($worker) {
                $result = $worker->network_balance * $worker->reward / 100;
                $worker->user()->increment('balance', $result);
                $change = $worker->network_balance - $result;

                foreach ($worker->children as $subWorker) {

                    $result = $change * $subWorker->reward / 100;
                    $subWorker->user()->increment('balance', $result);
                    $change = $change - $result;


                }

            })
        ;
    }
}
