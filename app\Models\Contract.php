<?php

namespace App\Models;

use App\ContractStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contract extends Model
{
    protected $table = 'contracts';

    protected $fillable = [
        'status_id', // pending, accept, reject, disqualified
        'user_id',
        'worker_id',

        'company_name',
        'comment', // დათხოვნის მიზეზი
        'user_comment', // წასვლის მიზეზი
        'end_date', // წასვლის მიზეზი
    ];

    protected $casts = [
        'status_id' => ContractStatus::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function worker(): BelongsTo
    {
        return $this->belongsTo(Worker::class);
    }

    public function points(): HasMany
    {
        return $this->hasMany(ContractPoint::class);
    }
}
