<?php

namespace App\Filament\Resources\ContractPointResource\Pages;

use App\Filament\Resources\ContractPointResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContractPoints extends ListRecords
{
    protected static string $resource = ContractPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
