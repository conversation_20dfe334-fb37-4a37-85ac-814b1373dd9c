<?php

namespace App\Http\Requests\Node\Contract;

use App\ContractStatus;
use App\Models\Contract;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConfirmRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'contract_id' => [
                'required',
                Rule::exists('contracts', 'id')->where(function ($query) {
                    $query
                        ->where('status_id', ContractStatus::Accepted)
                    ;
                }),
                function ($attribute, $value, $fail) {
                    $contract = Contract::query()
                        ->with('worker')
                        ->where('status_id', ContractStatus::Accepted)
                        ->find($value)
                    ;

                    if(!$contract)
                    {
                        return; // fallback to 'exists' rule
                    }

                    $te = $contract->worker->parent_id;
                    $exists = Contract::query()
                        ->where(function ($query) use ($contract, $te) {
                            $query
                                ->where('worker_id', $contract->worker_id)
                                ->orWhere('worker_id', $te)
                            ;
                        })
                        ->where('user_id', auth()->id())
                        ->exists()
                    ;

                    if (!$exists) {
                        $fail('not_found');
                    }
                },
            ],
            'comment' => 'required|string',
            'immediately' => 'nullable',
        ];
    }
}
