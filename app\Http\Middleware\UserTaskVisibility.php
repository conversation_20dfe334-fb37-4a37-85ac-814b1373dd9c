<?php

namespace App\Http\Middleware;

use App\Models\Worker;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class UserTaskVisibility
{
    public function handle(Request $request, Closure $next): Response
    {
        $workerIds = Worker::query()
            ->where('user_id', auth()->id())
            ->pluck('id')
        ;
        if ($workerIds->isEmpty()) {
            return response()->json(['error' => 'Access denied. No workers found.'], 403);
        }
        $directChildren = Worker::query()
            ->whereIn('parent_id', $workerIds)
            ->pluck('id')
        ;
        $accessibleIds = $directChildren->merge($workerIds) ;
        $targetWorkerId = $request->route('id') ?? $request->input('worker_id');
        if (!$accessibleIds->contains($targetWorkerId)) {
            return response()->json(['error' => 'Access denied. Worker not accessible.'], 403);
        }

        return $next($request);
    }
}
