<?php

namespace App\Filament\Resources\ContractPointResource\Pages;

use App\Filament\Resources\ContractPointResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContractPoint extends EditRecord
{
    protected static string $resource = ContractPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
