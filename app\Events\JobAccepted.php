<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class JobAccepted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $nodeLabel;
    public $parentNodeLabel;
    public $node_id;

    public function __construct(public $user_id) {}

    public function broadcastOn(): array
    {
        return [
            new Channel('user'. $this->user_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'JobAccepted';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'message' => $this->message,
            'nodeLabel' => $this->nodeLabel,
            'parentNodeLabel' => $this->parentNodeLabel,
            'user_id' => $this->user_id,
            'node_id' => $this->node_id,
        ];
    }
}
