# User Authentication API Documentation

## Base URL
```
http://your-domain.com/api
```

## Authentication
Most endpoints require authentication using Bearer token:
```
Authorization: Bearer {your-token}
```

## Endpoints

### 1. User Registration
**POST** `/register`

Register a new user with complete profile information.

**Request Body:**
```json
{
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>", 
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "+1234567890",
    "birth_date": "1990-01-01",
    "gender": "male",
    "address": "123 Main St",
    "city": "New York",
    "country": "USA"
}
```

**Response:**
```json
{
    "success": true,
    "message": "User registered successfully",
    "data": {
        "user": {...},
        "token": "your-auth-token",
        "email_verification_required": true,
        "phone_verification_required": true,
        "video_upload_required": true
    }
}
```

### 2. User Login
**POST** `/login`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {...},
        "token": "your-auth-token",
        "profile_completed": true,
        "fully_verified": false
    }
}
```

### 3. User Logout
**POST** `/logout`
*Requires Authentication*

**Response:**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

### 4. Get User Profile
**GET** `/user`
*Requires Authentication*

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {...},
        "profile_completed": true,
        "fully_verified": false,
        "verification_status": {
            "email_verified": true,
            "phone_verified": false,
            "video_status": "pending",
            "video_verified": false
        }
    }
}
```

### 5. Update User Profile
**PUT** `/user`
*Requires Authentication*

**Request Body:** (all fields optional)
```json
{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "password_confirmation": "newpassword123",
    "phone": "+1234567890",
    "birth_date": "1990-01-01",
    "gender": "male",
    "address": "456 New St",
    "city": "Los Angeles",
    "country": "USA"
}
```

### 6. Upload Verification Video
**POST** `/user/video`
*Requires Authentication*

**Request Body:** (multipart/form-data)
```
video: [video file] (mp4, mov, avi, wmv - max 100MB)
```

**Response:**
```json
{
    "success": true,
    "message": "Video uploaded successfully and is pending review",
    "data": {
        "video_path": "verification_videos/1_abc123.mp4",
        "video_status": "pending",
        "profile_completed": true
    }
}
```

### 7. Send Email Verification
**POST** `/email/verify/send`

**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

### 8. Verify Email
**POST** `/email/verify/{user_id}/{token}`

### 9. Send Phone Verification
**POST** `/phone/verify/send`

**Request Body:**
```json
{
    "phone": "+1234567890"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Verification code sent to your phone",
    "expires_at": "2024-07-18T12:10:00Z"
}
```

### 10. Verify Phone
**POST** `/phone/verify`

**Request Body:**
```json
{
    "phone": "+1234567890",
    "code": "123456"
}
```

### 11. Forgot Password
**POST** `/forgot-password`

**Request Body:**
```json
{
    "email": "<EMAIL>"
}
```

### 12. Reset Password
**POST** `/reset-password`

**Request Body:**
```json
{
    "token": "reset-token",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

## Error Responses

All endpoints return errors in this format:
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    }
}
```

## Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 422: Validation Error
- 500: Server Error

## User Verification Process

1. **Registration**: User provides all required information
2. **Email Verification**: User must verify email address
3. **Phone Verification**: User must verify phone number with SMS code
4. **Video Upload**: User must upload 1-2 minute verification video
5. **Video Review**: Admin reviews video (AI validation to be added later)
6. **Full Verification**: User is fully verified and can use all features
