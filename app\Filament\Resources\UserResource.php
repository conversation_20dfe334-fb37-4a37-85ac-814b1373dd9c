<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Users';

    protected static ?string $modelLabel = 'User';

    protected static ?string $pluralModelLabel = 'Users';

    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Personal Information')
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\DatePicker::make('birth_date')
                            ->required()
                            ->maxDate(now()->subYears(16)),
                        Forms\Components\Select::make('gender')
                            ->options([
                                'male' => 'Male',
                                'female' => 'Female',
                                'other' => 'Other',
                            ])
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('address')
                            ->rows(2)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('country')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Account Information')
                    ->schema([
                        Forms\Components\TextInput::make('balance')
                            ->numeric()
                            ->step(0.0001)
                            ->default(0)
                            ->suffix('₾'),
                        Forms\Components\TextInput::make('rate')
                            ->numeric()
                            ->suffix('%')
                            ->helperText('User rating percentage'),
                        Forms\Components\TextInput::make('leave_count')
                            ->numeric()
                            ->default(0)
                            ->helperText('Number of times user left'),
                        Forms\Components\TextInput::make('reject_count')
                            ->numeric()
                            ->default(0)
                            ->helperText('Number of rejections'),
                    ])->columns(2),

                Forms\Components\Section::make('Verification & Status')
                    ->schema([
                        Forms\Components\Toggle::make('profile_completed')
                            ->default(false),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        Forms\Components\FileUpload::make('verification_video_path')
                            ->label('Verification Video')
                            ->acceptedFileTypes(['video/mp4', 'video/mov', 'video/avi'])
                            ->maxSize(102400) // 100MB
                            ->directory('verification_videos')
                            ->visibility('private'),
                        Forms\Components\Select::make('video_status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                            ])
                            ->default('pending'),
                        Forms\Components\Textarea::make('video_rejection_reason')
                            ->rows(2)
                            ->visible(fn (Forms\Get $get) => $get('video_status') === 'rejected')
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('Verification Timestamps')
                    ->schema([
                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('Email Verified At'),
                        Forms\Components\DateTimePicker::make('phone_verified_at')
                            ->label('Phone Verified At'),
                        Forms\Components\DateTimePicker::make('video_verified_at')
                            ->label('Video Verified At'),
                        Forms\Components\DateTimePicker::make('last_login_at')
                            ->label('Last Login At'),
                    ])->columns(2),

                Forms\Components\Section::make('Password')
                    ->schema([
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->dehydrateStateUsing(fn ($state) => filled($state) ? bcrypt($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255),
                    ])
                    ->visibleOn('create'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('balance')
                    ->numeric(decimalPlaces: 4)
                    ->sortable()
                    ->suffix(' ₾')
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('rate')
                    ->numeric()
                    ->sortable()
                    ->suffix('%')
                    ->alignEnd(),
                Tables\Columns\IconColumn::make('profile_completed')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('video_status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label('Email Verified')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->email_verified_at)),
                Tables\Columns\IconColumn::make('phone_verified_at')
                    ->label('Phone Verified')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->phone_verified_at)),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('video_status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),
                Filter::make('is_active')
                    ->label('Active Users')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->toggle(),
                Filter::make('profile_completed')
                    ->label('Profile Completed')
                    ->query(fn (Builder $query): Builder => $query->where('profile_completed', true))
                    ->toggle(),
                Filter::make('email_verified')
                    ->label('Email Verified')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at'))
                    ->toggle(),
                Filter::make('phone_verified')
                    ->label('Phone Verified')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('phone_verified_at'))
                    ->toggle(),
                Filter::make('video_verified')
                    ->label('Video Verified')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('video_verified_at'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ContractsRelationManager::class,
            RelationManagers\NotificationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
