<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractPointResource\Pages;
use App\Filament\Resources\ContractPointResource\RelationManagers;
use App\Models\ContractPoint;
use App\Models\Contract;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContractPointResource extends Resource
{
    protected static ?string $model = ContractPoint::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = 'Contract Points';

    protected static ?string $modelLabel = 'Contract Point';

    protected static ?string $pluralModelLabel = 'Contract Points';

    protected static ?string $navigationGroup = 'Work Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Point Information')
                    ->schema([
                        Forms\Components\Select::make('contract_id')
                            ->label('Contract')
                            ->relationship('contract', 'id')
                            ->getOptionLabelFromRecordUsing(fn (Contract $record) => "Contract #{$record->id} - {$record->user->first_name} {$record->user->last_name} ({$record->worker->position})")
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('user_id')
                            ->label('Evaluator User')
                            ->relationship('user', 'first_name')
                            ->getOptionLabelFromRecordUsing(fn (User $record) => "{$record->first_name} {$record->last_name} ({$record->email})")
                            ->searchable(['first_name', 'last_name', 'email'])
                            ->preload()
                            ->nullable()
                            ->helperText('User who gave this rating (optional)'),
                        Forms\Components\TextInput::make('point')
                            ->label('Rating Points')
                            ->numeric()
                            ->required()
                            ->minValue(0)
                            ->maxValue(10)
                            ->step(1)
                            ->helperText('Rating from 0 to 10'),
                        Forms\Components\TextInput::make('count')
                            ->label('Count')
                            ->numeric()
                            ->required()
                            ->default(0)
                            ->minValue(0)
                            ->helperText('Number of times this rating was given'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('comment')
                            ->rows(4)
                            ->columnSpanFull()
                            ->helperText('Optional comment about this rating'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('contract.id')
                    ->label('Contract ID')
                    ->sortable()
                    ->url(fn ($record) => route('filament.admin.resources.contracts.view', $record->contract)),
                Tables\Columns\TextColumn::make('contract.user.first_name')
                    ->label('Contract User')
                    ->formatStateUsing(fn ($record) => "{$record->contract->user->first_name} {$record->contract->user->last_name}")
                    ->searchable(['contract.user.first_name', 'contract.user.last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('contract.worker.position')
                    ->label('Position')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label('Evaluator')
                    ->formatStateUsing(fn ($record) => $record->user ? "{$record->user->first_name} {$record->user->last_name}" : 'System')
                    ->searchable(['user.first_name', 'user.last_name'])
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('point')
                    ->label('Rating')
                    ->colors([
                        'danger' => fn ($state) => $state <= 3,
                        'warning' => fn ($state) => $state > 3 && $state <= 6,
                        'success' => fn ($state) => $state > 6 && $state <= 8,
                        'primary' => fn ($state) => $state > 8,
                    ])
                    ->formatStateUsing(fn ($state) => $state . '/10')
                    ->sortable(),
                Tables\Columns\TextColumn::make('count')
                    ->numeric()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('comment')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('point')
                    ->label('Rating')
                    ->options([
                        0 => '0/10',
                        1 => '1/10',
                        2 => '2/10',
                        3 => '3/10',
                        4 => '4/10',
                        5 => '5/10',
                        6 => '6/10',
                        7 => '7/10',
                        8 => '8/10',
                        9 => '9/10',
                        10 => '10/10',
                    ]),
                Filter::make('high_rating')
                    ->label('High Rating (8+)')
                    ->query(fn (Builder $query): Builder => $query->where('point', '>=', 8))
                    ->toggle(),
                Filter::make('low_rating')
                    ->label('Low Rating (≤3)')
                    ->query(fn (Builder $query): Builder => $query->where('point', '<=', 3))
                    ->toggle(),
                SelectFilter::make('contract_id')
                    ->label('Contract')
                    ->relationship('contract', 'id')
                    ->searchable()
                    ->preload(),
                Filter::make('has_comment')
                    ->label('Has Comment')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('comment'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractPoints::route('/'),
            'create' => Pages\CreateContractPoint::route('/create'),
            'view' => Pages\ViewContractPoint::route('/{record}'),
            'edit' => Pages\EditContractPoint::route('/{record}/edit'),
        ];
    }
}
