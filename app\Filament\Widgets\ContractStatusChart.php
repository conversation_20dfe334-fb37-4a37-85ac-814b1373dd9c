<?php

namespace App\Filament\Widgets;

use App\Models\Contract;
use App\ContractStatus;
use Filament\Widgets\ChartWidget;

class ContractStatus<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Contract Status Distribution';

    protected function getData(): array
    {
        $pending = Contract::where('status_id', ContractStatus::Pending)->count();
        $accepted = Contract::where('status_id', ContractStatus::Accepted)->count();
        $rejected = Contract::where('status_id', ContractStatus::Rejected)->count();
        $disqualified = Contract::where('status_id', ContractStatus::Disqualified)->count();
        $leaveRequest = Contract::where('status_id', ContractStatus::LEAVE_REQUEST)->count();

        return [
            'datasets' => [
                [
                    'label' => 'Contract Status',
                    'data' => [$pending, $accepted, $rejected, $disqualified, $leaveRequest],
                    'backgroundColor' => [
                        '#eab308', // yellow - pending
                        '#22c55e', // green - accepted
                        '#ef4444', // red - rejected
                        '#6b7280', // gray - disqualified
                        '#3b82f6', // blue - leave request
                    ],
                ],
            ],
            'labels' => ['Pending', 'Accepted', 'Rejected', 'Disqualified', 'Leave Request'],
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
