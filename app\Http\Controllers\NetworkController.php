<?php

namespace App\Http\Controllers;

use App\ContractStatus;
use App\Events\NotificationEvent;
use App\Http\Requests\Node\AddRequest;
use App\Http\Requests\Node\Contract\ApplyRequest;
use App\Http\Requests\Node\Contract\ConfirmRequest;
use App\Http\Resources\Applicant\WorkerResource;
use App\Http\Resources\ApplicantResource;
use App\Http\Resources\NetworkResource;
use App\Models\Contract;
use App\Models\Task;
use App\Models\Worker;
use App\Services\WorkerService;
use App\TaskStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class NetworkController extends Controller
{
    const LUNCH_ROOT_ID = 934;

    public function __construct(public WorkerService $workerService) {}

    public function network()
    {
        $rootId = self::LUNCH_ROOT_ID;

        $authId = auth()->id() ?? 1;

        $personal =  $authId ? Worker::query()
            ->whereRelation('contract', 'user_id', $authId)
            ->with([
                'contract',
                'parent.contract.user',
                'children.contract.user',

                'contracts',
                'children.contracts' => function ($query) {
                    $query->whereIn('status_id', [
                        ContractStatus::Accepted,
                        ContractStatus::Pending,
                        ContractStatus::LEAVE_REQUEST
                    ]);
                },
            ])
            ->descendantsAndSelf($rootId)
            : null
        ;

        $general = Worker::query()
            ->with(['children'])
            ->withExists(['contracts' => function ($query) {
                $query
                    ->where('user_id', auth()->id())
                    ->where('status_id', ContractStatus::Pending)
                ;
            }])
            ->descendantsAndSelf($rootId)
            ->toTree()
        ;
        return NetworkResource::collection($general)->additional([
            'personal' => $personal,
        ]);
    }

    public function addNode(AddRequest $request): JsonResponse
    {
        $child = $this->workerService->addNode(
            $request->input('node_id'),
            $request->position,
            $request->description,
            $request->reward,
        );

        return response()->json(['success' => true, 'node' => $child], 201);
    }


    public function applyNode(ApplyRequest $request): JsonResponse
    {
        $validatedWorkerId = $request->node_id;

        // ჯერ უნდა შემოწმდეს დარეჯექდებულია თუარა ამ ქსელიდან, და ასევე აქვს თუარა მიმდინარე პოზიცია ამავე ქსელში
        Contract::query()->create([
            'user_id' => auth()->id(),
            'worker_id' => $validatedWorkerId,
            'status_id' => ContractStatus::Pending,
        ]);

        return response()->json(['success' => true], 201);
    }

    public function confirmApplicant(Request $request): JsonResponse
    {
        $request->validate([
            'contract_id' => [
                'required',
                'exists:contracts,id',
                function ($attribute, $value, $fail) {
                    $contract = Contract::query()->find($value);

                    if(!$contract)
                    {
                        return ;
                    }

                    $te = $contract->worker->parent_id;
                    $exists = Contract::query()->where('worker_id', $te)->where('user_id', auth()->id())->exists();

                    if (!$exists) {
                        $fail('not_found');
                    }
                },
            ],
        ]);

        $contractId = $request->post('contract_id');

        $contract = Contract::query()->with('worker')->find($contractId);

        $contract?->update([
            'status_id' => ContractStatus::Accepted,
        ]);

        Contract::query()
            ->where('worker_id', $contract->worker_id)
            ->where('id', '!=', $contract->id)
            ->update([
                'status_id' => ContractStatus::Disqualified,
            ])
        ; // need notification after this




        $worker = $contract->worker;

        $worker->update([
            'contract_id' => $contract->id,
            'pointer_status_id' => $worker->tasks()->where('is_active', true)->exists() ? 1 : 2
        ]);

        if ($worker->tasks()->exists())
        {
            $worker
                ->tasks()
                ->update([
                    'user_id' => $contract->user_id,
                ])
            ;
            // ყველა მიიბი მარა ზოგი არდაიწყო ეხლა
            $worker
                ->tasks()
                ->where('is_active', true)
                ->where('status_id', TaskStatus::STATUS_STARTED)
                ->update([
                    'started_at' => now(),
                ])
            ;
        }

        event(new NotificationEvent(
            $contract->user_id,
            true,
            3,
            null,
            ''
        ));

        // ქვემდგომების მობმა აღარაა პრობლემა

        return response()->json(['success' => true], 201);
    }

    public function applicants($id=null)
    { // master function
        $workerId = self::LUNCH_ROOT_ID; // future filter

        $data = $this->workerService->applicantData($id, $workerId);

        return ApplicantResource::collection($data);
    }

    public function applies()
    {
        $workerId = self::LUNCH_ROOT_ID; // future filter

        $data = $this->workerService->appliesData($workerId);

        return WorkerResource::collection($data);

    }

    public function fired(ConfirmRequest $request): JsonResponse
    {
        // tu adminixar - options: imediatelly, TascComplite first
        // tu qvemdgomixar only one option: TascComplite first
        $contractId = $request->post('contract_id');
        $immediately = $request->post('immediately') == 'true';

        $contract = Contract::query()->find($contractId);

        $isAdminAction = $contract->user_id != auth()->id();


        if ($isAdminAction)
        {
            $statusId = $immediately
                ? ContractStatus::Rejected
                : ContractStatus::LEAVE_REQUEST
            ;
        }else{
            $statusId = ContractStatus::LEAVE_REQUEST;
        }


        $contract->update([
                'status_id' => $statusId,
                'comment' => $request->post('comment'),
            ])
        ;

        ////
        ///

        if($statusId == ContractStatus::Rejected)
        {
            $contract->user()->increment('reject_count');

            $contract->worker()->update([
                'contract_id' => null,
                'pointer_status_id' => 3,
            ]);

            $contract->update([
                'end_date' => now(),
                'comment' => $request->post('comment'),
            ]);

            $workerSubWorkerId = $contract->worker->children()->pluck('id');

            Task::query()
                ->where('status_id', TaskStatus::STATUS_STARTED)
                ->whereIn('worker_id', $workerSubWorkerId)
                ->update([
                    'status_id' => TaskStatus::STATUS_POSTPONED,
                ])
            ;

        }
        elseif($contract->worker->tasks()->where('is_active', true)->where('status_id', TaskStatus::STATUS_STARTED)->count() < 1){
            $contract->user()->increment('leave_count');

            $contract->worker()->update([
                'contract_id' => null,
                'pointer_status_id' => 3,
            ]);

            $contract->update([
                'end_date' => now(),
                'comment' => $request->post('comment'),
            ]);

            $workerSubWorkerId = $contract->worker->children()->pluck('id');

            Task::query()
                ->where('status_id', TaskStatus::STATUS_STARTED)
                ->whereIn('worker_id', $workerSubWorkerId)
                ->update([
                    'status_id' => TaskStatus::STATUS_POSTPONED,
                ])
            ;

        }


        return response()->json(['success' => true], 201);

    }
}
