<div class="space-y-6">
    @if($record->children()->count() > 0)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Child Workers</h3>
                <p class="text-sm text-gray-500 mt-1">Manage workers directly under {{ $record->position }}</p>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reward %</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Children</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($record->children as $child)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $child->position }}</div>
                                    <div class="text-sm text-gray-500">{{ $child->slug }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($child->user)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ $child->user->name }}
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Unassigned
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($child->reward, 3) }}%
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $child->children()->count() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                    <a href="{{ \App\Filament\Resources\WorkerResource::getUrl('view', ['record' => $child]) }}" 
                                       class="text-blue-600 hover:text-blue-900">View</a>
                                    <a href="{{ \App\Filament\Resources\WorkerResource::getUrl('edit', ['record' => $child]) }}" 
                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    <button type="button" 
                                            onclick="detachChild({{ $child->id }})"
                                            class="text-red-600 hover:text-red-900">Detach</button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.196-2.196M17 20v-2a3 3 0 00-3-3H8a3 3 0 00-3 3v2m14 0h-5m-9 0H3m9-10a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No child workers</h3>
            <p class="mt-1 text-sm text-gray-500">This worker doesn't have any children in the hierarchy.</p>
        </div>
    @endif

    @if($record->descendants()->count() > $record->children()->count())
        <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Hierarchy Information</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>This worker has <strong>{{ $record->children()->count() }}</strong> direct children and <strong>{{ $record->descendants()->count() }}</strong> total descendants in the hierarchy.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function detachChild(childId) {
    if (confirm('Are you sure you want to detach this child worker? This will move it to root level.')) {
        // This would need to be implemented with Livewire or AJAX
        // For now, we'll show a notification that this feature needs implementation
        alert('Detach functionality needs to be implemented with Livewire actions.');
    }
}
</script>
