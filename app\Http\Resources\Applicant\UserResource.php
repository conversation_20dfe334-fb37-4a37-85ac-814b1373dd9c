<?php

namespace App\Http\Resources\Applicant;

use App\Http\Resources\ApplicantResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
//            'first_name' => $this->first_name,
//            'last_name' => $this->last_name,
//            'birth_date' => $this->birth_date,
//            'gender' => $this->gender,
//            'created_at' => $this->created_at,
//            'country' => $this->country,
//            'city' => $this->city,
            'leave_count' => $this->leave_count,
            'reject_count' => $this->reject_count,
            'rate' => $this->rate,
//            'contracts' => ApplicantResource::collection($this->whenLoaded('contracts')),

        ];
    }
}
