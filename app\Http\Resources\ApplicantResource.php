<?php

namespace App\Http\Resources;

use App\Http\Resources\Applicant\PointResource;
use App\Http\Resources\Applicant\UserResource;
use App\Http\Resources\Applicant\WorkerResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ApplicantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status_id' => $this->status_id,
            'company_name' => $this->company_name,
            'comment' => $this->comment,
            'worker_id' => $this->worker_id,
            'created_at' => $this->created_at,
            'user' => new UserResource($this->whenLoaded('user')),
            'points' => $this->points,
//            'worker' => new WorkerResource($this->whenLoaded('worker')),
        ];
    }
}
