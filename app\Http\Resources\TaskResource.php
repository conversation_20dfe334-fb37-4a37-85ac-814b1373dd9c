<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'is_active' => $this->is_active,
            'status_id' => $this->status_id, //
            'rate' => $this->rate,
            'started_at' => $this->started_at,
            'duration' => $this->duration,
            'type' => $this->type,
            'weekday' => $this->weekday,
            'hour' => $this->hour,
            'minute' => $this->minute,
            'second' => $this->second,
            'input_text' => $this->task_description,
            'input_file' => $this->task_file,
            'output_text' => $this->result_description,
            'output_file' => $this->result_file,

            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
