<?php

namespace App\Filament\Resources\EmailVerificationResource\Pages;

use App\Filament\Resources\EmailVerificationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEmailVerification extends EditRecord
{
    protected static string $resource = EmailVerificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
