<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    { // bma shems<PERSON><PERSON>belsada mbrdzanebels shoris

        // romel kompaniashi ra statysi da ra pointebi aqvs exla
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('status_id')->index()->default(1);

            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('worker_id')->index()->constrained('workers')->cascadeOnDelete();


            $table->string('company_name')->nullable();
            $table->text('comment')->nullable();
            $table->text('user_comment')->nullable();

            $table->dateTime('end_date')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
