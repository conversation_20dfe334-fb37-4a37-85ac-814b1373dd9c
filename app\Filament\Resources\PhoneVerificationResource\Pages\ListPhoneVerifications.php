<?php

namespace App\Filament\Resources\PhoneVerificationResource\Pages;

use App\Filament\Resources\PhoneVerificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPhoneVerifications extends ListRecords
{
    protected static string $resource = PhoneVerificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
