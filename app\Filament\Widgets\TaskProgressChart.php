<?php

namespace App\Filament\Widgets;

use App\Models\Task;
use App\TaskStatus;
use Filament\Widgets\ChartWidget;

class TaskProgress<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Task Progress Overview';

    protected function getData(): array
    {
        $started = Task::where('status_id', TaskStatus::STATUS_STARTED)->count();
        $completed = Task::where('status_id', TaskStatus::STATUS_COMPLETED)->count();
        $postponed = Task::where('status_id', TaskStatus::STATUS_POSTPONED)->count();
        $failed = Task::where('status_id', TaskStatus::STATUS_FAILED)->count();

        return [
            'datasets' => [
                [
                    'label' => 'Task Status',
                    'data' => [$started, $completed, $postponed, $failed],
                    'backgroundColor' => [
                        '#3b82f6', // blue - started
                        '#22c55e', // green - completed
                        '#eab308', // yellow - postponed
                        '#ef4444', // red - failed
                    ],
                ],
            ],
            'labels' => ['Started', 'Completed', 'Postponed', 'Failed'],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
