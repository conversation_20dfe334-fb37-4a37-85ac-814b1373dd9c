<?php

namespace App\Services;

use App\ContractStatus;
use App\Models\Contract;
use App\Models\Worker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WorkerService
{
    public function network($rootId)
    {
        //        $te = Worker::query()->get();
//        foreach ($te as $t) {
//            $t->contracts()->create([
//                'status_id' => ContractStatus::Pending,
//                'user_id' => auth()->id(),
//                'worker_id' => $specificRootId,
//            ]);
//        }

        $authUserId = auth()->id();


        $t =  Worker::query()
            ->with([
                'children',
                'activeContract',
                'user' => function ($query) use ($authUserId) { // ქვემდგომი ნოუდებიდან რომელიმე თუ ეკუთვნის
                    $query
                        ->select([
                            'id',
                            'leave_count',
                            'reject_count',
                            'rate',
                        ])
                        ->whereRelation('workers', 'parent_master_user_id', $authUserId)
                    ;
                }
            ])
            ->withCount(['contracts as pending_contracts_count' => function ($query) {
                $query->where('status_id', ContractStatus::Pending)
                    ->whereIn('worker_id', function ($sub) {
                        $sub->select('id')
                            ->from('workers')
                            ->where('parent_master_user_id', auth()->id());
                    });
            }])
            ->descendantsAndSelf($rootId)
            ->toTree();


//        dd($t->toArray());

        return $t;
    }

    public function addNode($parentId, $position, $description, $reward)
    {
        $parent = Worker::query()->find($parentId);;

        $child = new Worker([
            'position' => $position,
            'slug' => Str::slug($position),
            'description' => $description,
            'reward' => $reward,
            'pointer_status_id' => 3,
            // root id?
        ]);

        $parent->appendNode($child);

        return $child;
    }

    public function applicantData($id=null, $workerId=null)
    {
        $authUserId = auth()->id() ?? 1;
        // root_id_db
        $childrenIds = Worker::query()
            ->where('user_id', $authUserId)
            ->with(['children'])
            ->descendantsAndSelf($workerId)
//            ->children()
            ?->first()
            ?->children
            ?->pluck('id') ?? null
        ;

//        dd($childrenIds);

        if (!$childrenIds)
        {
            return [];
        }


        return Contract::query()
            ->where('status_id', ContractStatus::Pending)
            ->whereIn('worker_id', $childrenIds)
            ->with([
                'worker',
                'user' => function ($query) {
                    $query
                        ->with([
                            'contracts.points',
//                            'contracts' => function ($query) {
//                                $query->limit(11);
//                            }
                        ]) // need hide data
                        ->select([
                            'id',
                            'first_name',
                            'last_name',
                            'birth_date',
                            'gender',
                            'created_at',
                            'country',
                            'city',
                            'leave_count',
                            'reject_count',
                            'rate',
                        ])
                    ;
                }
            ])
            ->when($id, function ($query, $id) {
                return $query->where('worker_id', $id);
            })
            ->when(is_null($id), function ($query) use ($childrenIds) {
                return $query->whereIn('worker_id', $childrenIds);
            })
            ->paginate(10)
        ;
    }

    public function appliesData($workerId)
    {
        $authUserId = auth()->id() ?? 1;
        // root_id_db
        $subWorkers = Worker::query()
            ->where('user_id', $authUserId)
            ->with(['children', 'children.contracts' => function ($query) {
                $query
                    ->where('status_id', ContractStatus::Pending)
                    ->where('user_id', '!=', auth()->id())
                    ->with(['user' => function ($query) {
                        $query
                            ->with([
                                'contracts.points',
                                'contracts' => function ($query) {
                                    $query->limit(11);
                                }
                            ]) // need hide data
                            ->select([
                                'id',
                                'first_name',
                                'last_name',
                                'birth_date',
                                'gender',
                                'created_at',
                                'country',
                                'city',
                                'leave_count',
                                'reject_count',
                                'rate',
                            ])
                        ;
                    }])
                ;
            }])
            ->descendantsAndSelf($workerId)
//            ->children()
            ?->first()
            ?->children ?? null
        ;

        return $subWorkers;
    }
}
