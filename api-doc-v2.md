# API Documentation v2

## Network Management Endpoints

### 1. GET `/network`
**მიზანი:** ქსელის იერარქიული სტრუქტურის მიღება

**Authentication:** <PERSON><PERSON> (Sanctum)

**Parameters:** არ სჭირდება

**Response:**
```json
[
  {
    "id": 1,
    "position": "Manager",
    "slug": "manager",
    "description": "Position description",
    "user_id": 123,
    "reward": 1000,
    "is_mine": true,
    "parent_id": null,
    "pointer_status_id": 1,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z",
    "contracts_count": 5,
    "children": [...],
    "node_user": {
      "id": 123,
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "birth_date": "1990-01-01",
      "gender": "male",
      "created_at": "2024-01-01T00:00:00.000000Z",
      "country": "Georgia",
      "city": "Tbilisi",
      "leave_count": 0,
      "rate": 4.5
    }
  }
]
```

---

### 2. POST `/add-node`
**მიზანი:** ახალი ნოუდის (პოზიციის) დამატება ქსელში

**Authentication:** Bear<PERSON> (Sanctum)

**Request Body:**
```json
{
  "node_id": 1,
  "position": "Developer",
  "description": "Software Developer Position",
  "reward": 800
}
```

**Validation:**
- `node_id`: აუცილებელი, უნდა არსებობდეს workers ცხრილში, მაქსიმუმ 10 ქვემდგომი
- `position`: აუცილებელი
- `reward`: აუცილებელი

**Response:**
```json
{
  "success": true,
  "node": {
    "id": 2,
    "position": "Developer",
    "slug": "developer",
    "description": "Software Developer Position",
    "reward": 800,
    "pointer_status_id": 3,
    "parent_master_user_id": 123
  }
}
```

---

### 3. POST `/node/apply`
**მიზანი:** კონკრეტულ ნოუდზე (პოზიციაზე) განაცხადის შეტანა

**Authentication:** Bearer Token (Sanctum)

**Request Body:**
```json
{
  "node_id": 1
}
```

**Validation:**
- `node_id`: აუცილებელი, უნდა არსებობდეს workers ცხრილში
- შემოწმება: მომხმარებელი არ უნდა იყოს ამ იერარქიის ნაწილი (არც წინაპარი, არც შთამომავალი)
- შემოწმება: მომხმარებელი არ უნდა იყოს დარეჯექთებული ამ ქსელიდან

**Response:**
```json
{
  "success": true
}
```

---

### 4. GET `/applicants/{id?}`
**მიზანი:** განმცხადებლების სიის მიღება (კონკრეტული ნოუდისთვის ან ყველასთვის)

**Authentication:** Bearer Token (Sanctum)

**Parameters:**
- `id` (optional): worker_id - კონკრეტული ნოუდის განმცხადებლები

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "status_id": 1,
      "company_name": "Company Name",
      "comment": "Application comment",
      "worker_id": 1,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "user": {
        "id": 123,
        "first_name": "John",
        "last_name": "Doe",
        "birth_date": "1990-01-01",
        "gender": "male",
        "created_at": "2024-01-01T00:00:00.000000Z",
        "country": "Georgia",
        "city": "Tbilisi",
        "leave_count": 0,
        "rate": 4.5,
        "contracts": [...]
      },
      "points": [...],
      "worker": {
        "id": 1,
        "position": "Manager",
        "description": "Position description"
      }
    }
  ],
  "links": {...},
  "meta": {...}
}
```

---

### 5. POST `/node/applicant/confirm`
**მიზანი:** განმცხადებლის დადასტურება/მიღება

**Authentication:** Bearer Token (Sanctum)

**Request Body:**
```json
{
  "contract_id": 1,
  "comment": "Confirmation comment",
  "immediately": false
}
```

**Validation:**
- `contract_id`: აუცილებელი, უნდა არსებობდეს contracts ცხრილში და ეკუთვნოდეს ავტორიზებულ მომხმარებელს
- `comment`: აუცილებელი, string
- `immediately`: არასავალდებულო, boolean

**Response:**
```json
{
  "success": true
}
```

**ფუნქციონალი:**
- კონტრაქტის სტატუსი იცვლება "Accepted"-ზე
- სხვა განაცხადები ამ ნოუდზე ხდება "Disqualified"
- Worker-ს ენიჭება user_id
- ყველა დავალება გადაეცემა ახალ მომხმარებელს
- ქვემდგომი ნოუდები განახლდება

---

### 6. POST `/fired`
**მიზანი:** თანამშრომლის გათავისუფლება

**Authentication:** Bearer Token (Sanctum)

**Request Body:**
```json
{
  "contract_id": 1,
  "comment": "Termination reason",
  "immediately": true
}
```

**Validation:**
- `contract_id`: აუცილებელი, უნდა არსებობდეს contracts ცხრილში
- `comment`: აუცილებელი, string
- `immediately`: არასავალდებულო, boolean

**Response:**
```json
{
  "success": true
}
```

**ლოგიკა:**
- ადმინისთვის: `immediately=true` - მყისიერი გათავისუფლება, `false` - დავალების დასრულების შემდეგ
- ქვემდგომისთვის: მხოლოდ დავალების დასრულების შემდეგ გათავისუფლება

---

## ზოგადი ინფორმაცია

**Base URL:** `/api/`

**Authentication:** ყველა endpoint საჭიროებს Sanctum Bearer Token-ს

**Error Responses:**
```json
{
  "message": "Error message",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

**HTTP Status Codes:**
- 200: წარმატებული GET მოთხოვნა
- 201: წარმატებული POST მოთხოვნა (შექმნა)
- 401: არაავტორიზებული
- 422: ვალიდაციის შეცდომა
- 500: სერვერის შეცდომა
