<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) { // input
            $table->id();
            // type ?
            $table->boolean('is_active')->default(true);

            $table->unsignedTinyInteger('status_id')->default(0);
            $table->unsignedTinyInteger('rate')->nullable()->comment('task-done rate written, else task can not done');
//            rate, tu dabalia naxui, tuarada ylea patroni da fuls uxdis ;d anu magali nishnavs mis samsaxurshi shenarchunebas

            // aq weria is tu visi taskia, tore vin adzlevs tasks eg ashkaraa
            $table->foreignId('worker_id')->index()->constrained('workers')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('user_id')->nullable()->index()->constrained('users')->nullOnDelete();

            $table->dateTime('started_at')->nullable();
            $table->unsignedTinyInteger('type')->default(0)->comment('0: timelapse, 1: perlapse');
            $table->unsignedInteger('duration');

            $table->unsignedTinyInteger('weekday')->nullable(); // 0=Sunday, 1=Monday, ..., 6=Saturday
            $table->unsignedTinyInteger('hour')->nullable();    // 0 - 23
            $table->unsignedTinyInteger('minute')->nullable();  // 0 - 59
            $table->unsignedTinyInteger('second')->nullable();  // 0 - 59

            $table->text('task_description');
            $table->string('task_file')->nullable();

            $table->text('result_description')->nullable();
            $table->text('result_file')->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
