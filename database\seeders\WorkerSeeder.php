<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Worker;

class WorkerSeeder extends Seeder
{
    public array $data;

    public function __construct()
    {
        $this->data = [
            [
                'position' => 'make money',
                'slug' => 'make_money',
                'reward' => round(mt_rand(0, 100) / 10, 1),
                'user_id' => 1,
                'children' => [
                    [
                        'position' => 'CEO',
                        'slug' => 'CEO',
                        'reward' => round(mt_rand(0, 100) / 10, 1),
                        'children' => [
                            [
                                'position' => 'CMO – Chief Marketing Officer',
                                'slug' => 'CMO',
                                'reward' => round(mt_rand(0, 100) / 10, 1),
                                'children' => [
                                    [
                                        'position' => 'Head of Digital Marketing',
                                        'slug' => 'head_digital_marketing',
                                        'reward' => round(mt_rand(0, 100) / 10, 1),
                                    ],
                                    [
                                        'position' => 'Head of Brand Management',
                                        'slug' => 'head_brand_management',
                                        'reward' => round(mt_rand(0, 100) / 10, 1),
                                        'children' => [
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ]
                                    ],
                                    [
                                        'position' => 'Head of Market Research',
                                        'slug' => 'head_market_research',
                                        'reward' => round(mt_rand(0, 100) / 10, 1),
                                        'children' => [
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                            ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ],
                                    ]
                                ]
                            ],
                            [
                                'position' => 'CHRO – Chief Human Resources Officer',
                                'slug' => 'CHRO',
                                'reward' => round(mt_rand(0, 100) / 10, 1),
                                'children' => [
                                        ['position' => 'Recruitment Manager', 'slug' => 'recruitment_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Onboarding Coordinator', 'slug' => 'onboarding_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1),],
                                        ['position' => 'HR Business Partner', 'slug' => 'hr_business_partner', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Payroll Specialist', 'slug' => 'payroll_specialist', 'reward' => round(mt_rand(0, 100) / 10, 1),],
                                        ['position' => 'Training & Development Manager', 'slug' => 'training_development_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Compensation & Benefits Manager', 'slug' => 'compensation_benefits_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Employee Relations Manager', 'slug' => 'employee_relations_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Talent Acquisition Specialist', 'slug' => 'talent_acquisition_specialist', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'HR Analytics Lead', 'slug' => 'hr_analytics_lead', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Organizational Development Manager', 'slug' => 'organizational_development_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Diversity & Inclusion Officer', 'slug' => 'diversity_inclusion_officer', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'HR Compliance Officer', 'slug' => 'hr_compliance_officer', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Performance Manager', 'slug' => 'performance_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'HR Generalist', 'slug' => 'hr_generalist', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Benefits Analyst', 'slug' => 'benefits_analyst', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Labor Relations Specialist', 'slug' => 'labor_relations_specialist', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Learning Experience Designer', 'slug' => 'learning_experience_designer', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Succession Planning Lead', 'slug' => 'succession_planning_lead', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Workplace Wellness Coordinator', 'slug' => 'workplace_wellness_coordinator', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Remote Work Specialist', 'slug' => 'remote_work_specialist', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                        ['position' => 'Internship Program Manager', 'slug' => 'internship_program_manager', 'reward' => round(mt_rand(0, 100) / 10, 1)],
                                ],

                            ],
                            [
                                'position' => 'CIO – Chief Information Officer',
                                'slug' => 'CIO',
                                'reward' => round(mt_rand(0, 100) / 10, 1),
                                'children' => [
                                    [
                                        'position' => 'IT Infrastructure Manager',
                                        'slug' => 'it_infrastructure_manager',
                                        'reward' => round(mt_rand(0, 100) / 10, 1),
                                    ],
                                ]
                            ]

                        ]
                    ]
                ],
            ]
        ];
    }


    public function run(): void
    {
        foreach ($this->data as $item) {
            $this->createWithChildren($item);
        }
    }

    private function createWithChildren(array $data, Worker $parent = null): Worker
    {
        // მშობლის გარეშე ვქმნით
        $worker = new Worker([
            'position' => $data['position'],
            'slug' => $data['slug'],
            'reward' => $data['reward'],
            'user_id' => $data['user_id'] ?? null,
        ]);

        // თუ მშობელი არსებობს, ვუკავშირებთ
        if ($parent) {
            $worker = $parent->children()->save($worker);
        } else {
            $worker->save();
        }

        // თუ შვილები ჰყავს, ვამუშავებთ რეკურსიულად
        if (!empty($data['children']) && is_array($data['children'])) {
            foreach ($data['children'] as $child) {
                $this->createWithChildren($child, $worker);
            }
        }

        return $worker;
    }

}
